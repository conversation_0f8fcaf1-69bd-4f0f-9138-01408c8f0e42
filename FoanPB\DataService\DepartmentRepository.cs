﻿using Dapper;
using FoanPB.Models;
using System.Data;
using System.Data.OleDb;

namespace FoanPB.DataService
{
    public class DepartmentRepository
    {
        private readonly string _connectionString;

        public DepartmentRepository(string connectionString)
        {
            _connectionString = connectionString;
        }


        private IDbConnection Connection => new OleDbConnection(_connectionString);

        public IEnumerable<Department> GetAllDepartments()
        {
            using (IDbConnection dbConnection = Connection)
            {
                const string query = "SELECT DepartmentID, DepartmentName FROM tblDepartment";
                return dbConnection.Query<Department>(query);
            }
        }


        public void AddDepartment(Department department)
        {
            using (IDbConnection dbConnection = Connection)
            {
                dbConnection.Open(); // Ensure the connection is open

                using (var transaction = dbConnection.BeginTransaction())
                {
                    try
                    {
                        // 1. Execute the INSERT statement
                        const string insertQuery = "INSERT INTO tblDepartment (DepartmentName) VALUES (@DepartmentName)";
                        dbConnection.Execute(insertQuery, department, transaction: transaction);

                        // 2. Retrieve the last inserted DepartmentID
                        const string identityQuery = "SELECT @@IDENTITY";
                        var id = dbConnection.QuerySingle<int>(identityQuery, transaction: transaction);

                        // 3. Set the DepartmentID on the department object
                        department.DepartmentID = id;

                        // Commit the transaction
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        // Rollback the transaction in case of an error
                        transaction.Rollback();
                        // Optionally, log the exception or rethrow
                        throw new Exception("An error occurred while adding the department.", ex);
                    }
                }
            }
        }

        public void UpdateDepartment(Department department)
        {
            using (IDbConnection dbConnection = Connection)
            {
                const string query = "UPDATE tblDepartment SET DepartmentName = @DepartmentName WHERE DepartmentID = @DepartmentID";
                //dbConnection.Execute(query, department);
                var rowsAffected = dbConnection.Execute(query, new { department.DepartmentName, department.DepartmentID });

                // Optional: Check if the update succeeded
                if (rowsAffected == 0)
                {
                    // Handle the case where no record was updated
                }
            }
        }

        public void DeleteDepartment(int departmentId)
        {
            using (IDbConnection dbConnection = Connection)
            {
                const string query = "DELETE FROM tblDepartment WHERE DepartmentID = @DepartmentID";
                dbConnection.Execute(query, new { DepartmentID = departmentId });
            }
        }
    }
}
