﻿using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace FoanPB.Service
{
    public class DepartmentIDToNameMultiConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] is int departmentID && values[1] is IEnumerable<Department> departments)
            {
                var department = departments.FirstOrDefault(d => d.DepartmentID == departmentID);
                return department != null ? department.DepartmentName : "Unknown";
            }
            return "Unknown";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("Two-way binding is not supported.");
        }
    }
}
