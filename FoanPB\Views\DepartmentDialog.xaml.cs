﻿using FoanPB;
using FoanPB.DataService;
using FoanPB.Service;
using FoanPB.ViewModels;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for DepartmentDialog.xaml
    /// </summary>
    public partial class DepartmentDialog : Window
    {
        public DepartmentDialog()
        {
            InitializeComponent();
            string connectionString = ConfigurationManager.ConnectionStrings["AccessDbConnection"].ConnectionString;
            DepartmentRepository departmentRepository = new DepartmentRepository(connectionString);
            this.DataContext = new DepartmentViewModel(departmentRepository);
            
        }
    }
}

