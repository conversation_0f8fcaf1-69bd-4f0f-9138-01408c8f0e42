﻿using FoanPB.Commands;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private BaseViewModel _selectedViewModel;

		public BaseViewModel SelectedViewModel
        {
			get { return _selectedViewModel; }
			set 
			{
				_selectedViewModel = value;
				OnPropertyChanged(nameof(SelectedViewModel));
			}
		}

        //private readonly SharedDataService _sharedDataService;
        private readonly IServiceProvider _serviceProvider;

        public ICommand UpdateViewCommand { get; set; }
		public MainViewModel(IServiceProvider serviceProvider)
		{
            //UpdateViewCommand = new UpdateViewCommand(this);
            //_sharedDataService = sharedDataService;
            _serviceProvider = serviceProvider;
            UpdateViewCommand = new UpdateViewCommand(this, serviceProvider);
            //OpenOperatorViewCommand = new RelayCommand(OpenOperatorView);

        }

    }
}
