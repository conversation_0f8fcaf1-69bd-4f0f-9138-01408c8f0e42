﻿using FoanPB.ViewModels;
using System.Windows;

namespace FoanPB
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow(MainViewModel mainViewModel)
        {
            InitializeComponent();
            //DataContext = new MainViewModel();
            DataContext = mainViewModel;
        }
    }
}