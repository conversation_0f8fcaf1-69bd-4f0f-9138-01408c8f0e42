﻿namespace FoanPB.Models
{

    public class ProductionStepTemplate
    {
        // Represents the auto-incrementing StepId field
        public int StepId { get; set; }

        // Represents the TemplateId field (foreign key or related to the Template table)
        public int TemplateId { get; set; }

        // Represents the StepNumber field
        public int StepNumber { get; set; }

        // Represents the StepDescription field
        public string StepDescription { get; set; }

        // Represents the EquipmentName field
        public string EquipmentName { get; set; }

        // Represents the estimated minutes field (Estmin)
        public int Estmin { get; set; }

        // Constructor (optional, for convenience)
        public ProductionStepTemplate() { }

        // Overloaded constructor (optional, for initialization)
        public ProductionStepTemplate(int stepId, int templateId, int stepNumber, string stepDescription, string equipmentName, int estmin)
        {
            StepId = stepId;
            TemplateId = templateId;
            StepNumber = stepNumber;
            StepDescription = stepDescription;
            EquipmentName = equipmentName;
            Estmin = estmin;
        }
    }

}
