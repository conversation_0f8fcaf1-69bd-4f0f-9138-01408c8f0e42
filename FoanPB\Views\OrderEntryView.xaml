﻿<UserControl x:Class="FoanPB.Views.OrderEntryView" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:local="clr-namespace:FoanPB.Views" xmlns:service="clr-namespace:FoanPB.Service" xmlns:sys="clr-namespace:System;assembly=mscorlib" mc:Ignorable="d" d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <service:ProductionStepToVisibilityConverter x:Key="ProductionStepToVisibilityConverter" />
        <service:TypeNameConverter x:Key="TypeNameConverter" />
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition />
        </Grid.RowDefinitions>
        <DockPanel Grid.Row="0">
            <Menu DockPanel.Dock="Top">
                <MenuItem Header="File">
                    <MenuItem Header="Manage Department" Command="{Binding OpenDepartmentPopupCommand}" />
                    <MenuItem Header="Manage New Employees" Command="{Binding DataContext.UpdateViewCommand, RelativeSource={RelativeSource AncestorType=Window}}" CommandParameter="Operator" />
                    <MenuItem Header="Manage Equipment" Command="{Binding OpenEquipmentPopupCommand}" />
                    <MenuItem Header="Manage Step Type" Command="{Binding OpenStepTypePopupCommand}" />
                    <Separator />
                    <MenuItem Header="Exit" Click="Exit_Click" />
                </MenuItem>
            </Menu>
        </DockPanel>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="Order Entry" FontSize="24" FontWeight="Bold" Margin="10" VerticalAlignment="Center" />
            <Button Grid.Column="1" Content="Back to Job List" Height="25" Width="120" Margin="10" Command="{Binding GoBackCommand}" />
        </Grid>
        <Grid Grid.Row="2" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="150" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="150" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <TextBlock Text="Job Number" Grid.Row="0" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding JobNumber, UpdateSourceTrigger=PropertyChanged}" Grid.Row="0" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Product Name" Grid.Row="0" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding ProductName, UpdateSourceTrigger=PropertyChanged}" Grid.Row="0" Grid.Column="3" Margin="5" Height="25" />
            <TextBlock Text="Description" Grid.Row="1" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}" Grid.Row="1" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Length" Grid.Row="1" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding Length, UpdateSourceTrigger=PropertyChanged}" Grid.Row="1" Grid.Column="3" Margin="5" Height="25" />
            <TextBlock Text="Width" Grid.Row="2" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding Width, UpdateSourceTrigger=PropertyChanged}" Grid.Row="2" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Job Type" Grid.Row="2" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <ComboBox Grid.Row="2" Grid.Column="3" Text="{Binding JobType, UpdateSourceTrigger=PropertyChanged}" Margin="5" Height="25" Width="120">
                <ComboBoxItem Content="Order" />
                <ComboBoxItem Content="Store" />
                <ComboBoxItem Content="Both" />
            </ComboBox>
            <TextBlock Text="Qty" Grid.Row="3" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding Qty, UpdateSourceTrigger=PropertyChanged}" Grid.Row="3" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Representative" Grid.Row="3" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <ComboBox Grid.Row="3" Grid.Column="3" Margin="5" Height="25" ItemsSource="{Binding Employees}" DisplayMemberPath="EmpName" SelectedValuePath="EmpName" SelectedValue="{Binding Rep, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
            <TextBlock Text="SKU" Grid.Row="4" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <TextBox Text="{Binding SKU, UpdateSourceTrigger=PropertyChanged}" Grid.Row="4" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Urgent" Grid.Row="4" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <CheckBox IsChecked="{Binding IsUrgent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Grid.Row="4" Grid.Column="3" Margin="5" />
            <TextBlock Text="Target Date" Grid.Row="5" Grid.Column="0" Margin="5" VerticalAlignment="Center" />
            <DatePicker SelectedDate="{Binding TargetDate}" Grid.Row="5" Grid.Column="1" Margin="5" Height="25" />
            <TextBlock Text="Template" Grid.Row="5" Grid.Column="2" Margin="5" VerticalAlignment="Center" />
            <ComboBox Name="CmbTemplate" Grid.Row="5" Grid.Column="3" Height="25" Width="120" Margin="5" ItemsSource="{Binding Templates}" SelectedItem="{Binding SelectedTemplate, Mode=TwoWay}" IsEditable="True" IsTextSearchEnabled="True" GotFocus="CmbTemplate_GotFocus" KeyDown="CmbTemplate_KeyDown">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100" />
                                <ColumnDefinition Width="60" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Border BorderBrush="Gray" BorderThickness="0,0,1,0" Grid.Column="0">
                                <TextBlock Text="{Binding TemplateName}" VerticalAlignment="Center" Margin="5,0" />
                            </Border>
                            <Border BorderBrush="Gray" BorderThickness="0,0,1,0" Grid.Column="1">
                                <TextBlock Text="{Binding SKU}" VerticalAlignment="Center" Margin="5,0" />
                            </Border>
                            <TextBlock Text="{Binding ProductName}" Grid.Column="2" VerticalAlignment="Center" Margin="5,0" />
                        </Grid>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <StackPanel Grid.Row="6" Grid.ColumnSpan="4" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Load Template" Height="25" Width="100" Margin="5" Command="{Binding LoadTemplateCommand}" />
                <Button Name="SaveTemplateButton" Content="Save Template" Height="25" Width="100" Margin="5" Click="SaveTemplateButton_Click" />
                <Button Name="DeleteTemplateButton" Content="Delete Template" Command="{Binding DeleteTemplateCommand}" Height="25" Width="100" Margin="5" />
                <Popup Name="SaveTemplatePopup" PlacementTarget="{Binding ElementName=SaveTemplateButton}" StaysOpen="True" AllowsTransparency="True" PopupAnimation="Fade">
                    <Border Background="White" BorderBrush="Tomato" BorderThickness="1" Padding="10" CornerRadius="3">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="Enter the template name" Margin="0,0,0,10" />
                            <TextBox Name="txtTmpName" Text="{Binding TemplateName, UpdateSourceTrigger=PropertyChanged}" />
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Name="SaveTemplateOK" Content="OK" Width="75" Margin="5" Click="SaveTemplateCancel_Click" Command="{Binding AddTemplateCommand}" CommandParameter="{Binding ElementName=SaveTemplatePopup}" />
                                <Button Name="SaveTemplateCancel" Content="Cancel" Width="75" Margin="5" Click="SaveTemplateCancel_Click" />
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Popup>
            </StackPanel>
        </Grid>
        <StackPanel Grid.Row="3">
            <TextBlock Text="Job Process Step" Margin="5" />
            <DataGrid x:Name="dataGridProductionSteps" Margin="5" ItemsSource="{Binding ProductionSteps}" AutoGenerateColumns="False" CanUserAddRows="True" IsReadOnly="False" SelectedItem="{Binding SelectedStep}" SelectionUnit="FullRow" CanUserSortColumns="False">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Step No." Binding="{Binding StepNumber, UpdateSourceTrigger=PropertyChanged}" IsReadOnly="True" />
                    <DataGridTemplateColumn Header="Step Type">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding StepDescription}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <ComboBox ItemsSource="{Binding Path=DataContext.StepTypes, RelativeSource={RelativeSource AncestorType=DataGrid}}" DisplayMemberPath="StepDescription" SelectedValuePath="StepDescription" SelectedValue="{Binding StepDescription, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding StepDescription}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="TargetDate">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock>
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding Path=StepTargetDate, StringFormat=d}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding StepTargetDate}" Value="{x:Null}">
                                                    <Setter Property="Text" Value="" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <DatePicker SelectedDate="{Binding Path=StepTargetDate, UpdateSourceTrigger=PropertyChanged}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Equipment">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding EquipmentName}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <ComboBox ItemsSource="{Binding DataContext.Equipments, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" DisplayMemberPath="EquipmentName" SelectedValuePath="EquipmentName" SelectedValue="{Binding EquipmentName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding EquipmentName}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Operator">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding OperatorName}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <ComboBox ItemsSource="{Binding DataContext.Employees, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" DisplayMemberPath="EmpName" SelectedValuePath="EmpName" SelectedValue="{Binding OperatorName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding EmpName}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="Estimated Minutes" Binding="{Binding Estmin}" />
                    <DataGridTemplateColumn Header="Actions">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="Delete" Margin="0 0 5 0" Padding="5 0 5 0" IsEnabled="True" Visibility="{Binding Path=., Converter={StaticResource ProductionStepToVisibilityConverter}}" Command="{x:Static DataGrid.DeleteCommand}" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
            <StackPanel Orientation="Horizontal" Margin="10">
                <Button x:Name="moveUpButton" Content="Move Up" Command="{Binding MoveUpCommand}" Margin="5" />
                <Button x:Name="moveDownButton" Content="Move Down" Command="{Binding MoveDownCommand}" Margin="5" />
                <Button Content="Save" Height="25" Width="100" HorizontalAlignment="Right" Margin="5" Command="{Binding SaveProductCommand}" />
                <Button Content="Clear" Height="25" Width="100" HorizontalAlignment="Right" Margin="5" Command="{Binding ClearInputFieldsCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
<!--// End of file: OrderEntryView.xaml-->