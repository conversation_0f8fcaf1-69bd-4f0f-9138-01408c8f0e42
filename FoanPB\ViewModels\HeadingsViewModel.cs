﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;
using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;

namespace FoanPB.ViewModels
{
    public class HeadingsViewModel : StepTypeViewModelBase
    {
        // Change target step description to match "Headings"
        protected override string TargetStepDescription => "Headings";

        private string _startTimeText;
        public string StartTimeText
        {
            get => _startTimeText;
            set
            {
                _startTimeText = value;
                OnPropertyChanged(nameof(StartTimeText));
            }
        }

        // Same update‐time command as in ProcessViewModel
        public ICommand UpdateToCurrentDateCommand { get; }

        public HeadingsViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
            UpdateToCurrentDateCommand = new RelayCommand(UpdateToCurrentDate);
        }

        private void UpdateToCurrentDate()
        {
            StartTimeText = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        }

        // Copy ProcessViewModel’s detailed‐filter override
        protected override bool FilterSearchByDetail(object obj)
        {
            if (SelectedDetailFilter == "Equipment")
            {
                if (obj is Product product)
                {
                    if (string.IsNullOrWhiteSpace(FilterDetailText)) return true;
                    var term = FilterDetailText.ToLowerInvariant();
                    return product.ProductionSteps.Any(step =>
                        !string.IsNullOrWhiteSpace(step.EquipmentName) &&
                        step.EquipmentName.ToLowerInvariant().Contains(term));
                }
                return false;
            }
            return base.FilterSearchByDetail(obj);
        }
    }
}
