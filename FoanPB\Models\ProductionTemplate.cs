﻿namespace FoanPB.Models
{
    public class ProductionTemplate
    {
        // Represents the auto-incrementing TemplateId field
        public int TemplateId { get; set; }

        // Represents the TemplateName field
        public string TemplateName { get; set; }

        // Represents the TemplateDescription field
        public string TemplateDescription { get; set; }
        public int Length { get; set; }
        public int Width { get; set; }

        // Represents the ProductName field
        public string ProductName { get; set; }

        // Represents the Description field
        public string Description { get; set; }

        // Represents the SKU field
        public string SKU { get; set; }

        // Navigation property to represent the one-to-many relationship with Step
        public ICollection<ProductionStepTemplate> ProductionStepTemplates
        {
            get; set;
        }

        // Constructor (optional, for convenience)
        public ProductionTemplate() 
        {
            ProductionStepTemplates = new List<ProductionStepTemplate>();
        }

        // Overloaded constructor (optional, for initialization)
        public ProductionTemplate(int templateId, string templateName, string templateDescription, string productName, string description, string sku)
        {
            TemplateId = templateId;
            TemplateName = templateName;
            TemplateDescription = templateDescription;
            ProductName = productName;
            Description = description;
            SKU = sku;
            ProductionStepTemplates = new List<ProductionStepTemplate>();
        }

        // Optional: Method to add a Step to the Template
        public void AddStep(ProductionStepTemplate step)
        {
            if (step != null)
            {
                step.TemplateId = this.TemplateId;
                ProductionStepTemplates.Add(step);
            }
        }

        public override string ToString()
        {
            return TemplateName;
        }
    }

}
