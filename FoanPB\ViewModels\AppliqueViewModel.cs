﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class AppliqueViewModel : StepTypeViewModelBase
    {
        // Specify the step description this view model handles
        protected override string TargetStepDescription => "Applique";

        public AppliqueViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
            // No additional commands or properties needed—StepTypeViewModelBase provides all core functionality
        }
    }
}
