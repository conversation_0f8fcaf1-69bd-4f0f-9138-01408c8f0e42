-- <PERSON><PERSON><PERSON> to create the tblTimeLogs table for the FoanPB application
-- This table stores individual time log entries for production steps
-- Run this script in your Microsoft Access database

CREATE TABLE tblTimeLogs (
    TimeLogId AUTOINCREMENT PRIMARY KEY,
    ProductionStepId LONG NOT NULL,
    EmployeeId LONG NOT NULL,
    LoggedDate DATETIME NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NOT NULL,
    DurationMinutes LONG NOT NULL,
    WorkDescription MEMO,
    Status TEXT(50) NOT NULL DEFAULT 'Doing',
    CreatedAt DATETIME NOT NULL DEFAULT Now()
);

-- Create indexes for better performance
CREATE INDEX IX_TimeLogs_ProductionStepId ON tblTimeLogs (ProductionStepId);
CREATE INDEX IX_TimeLogs_EmployeeId ON tblTimeLogs (EmployeeId);
CREATE INDEX IX_TimeLogs_LoggedDate ON tblTimeLogs (LoggedDate);

-- Add some sample data (optional - remove if not needed)
-- Note: Replace the ProductionStepId and EmployeeId values with actual IDs from your database
/*
INSERT INTO tblTimeLogs (ProductionStepId, EmployeeId, LoggedDate, StartTime, EndTime, DurationMinutes, WorkDescription, Status, CreatedAt)
VALUES 
    (1, 1, #2024-12-15#, #2024-12-15 09:00:00#, #2024-12-15 11:30:00#, 150, 'Initial cutting work completed', 'Done', Now()),
    (1, 2, #2024-12-15#, #2024-12-15 13:00:00#, #2024-12-15 14:45:00#, 105, 'Quality check and adjustments', 'Done', Now()),
    (2, 1, #2024-12-16#, #2024-12-16 08:30:00#, #2024-12-16 10:00:00#, 90, 'Setup and preparation', 'Doing', Now());
*/
