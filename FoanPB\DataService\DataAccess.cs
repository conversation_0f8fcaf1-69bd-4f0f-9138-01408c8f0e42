﻿using Dapper;
using FoanPB.Models;
using System.Configuration;
using System.Data;
using System.Data.OleDb;
using System.Windows;
namespace FoanPB.DataService
{
    public class DataAccess
    {
        private static string connectionString = ConfigurationManager.ConnectionStrings["AccessDbConnection"].ConnectionString;
        public async Task AddProductAsync(Product product)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        var insertSql = @"
                        INSERT INTO tblProducts 
                            ([jobNumber], [productname], [description], [Length], [Width], [jobtype], [Qty], [Rep], [SKU], [IsUrgent], [TargetDate])
                        VALUES
                            (@JobNumber, @ProductName, @Description, @Length, @Width, @JobType, @Qty, @Rep, @SKU, @IsUrgent, @TargetDate)";
                        await connection.ExecuteAsync(
                            insertSql,
                            new
                            {
                                product.JobNumber,
                                product.ProductName,
                                product.Description,
                                product.Length,
                                product.Width,
                                product.JobType,
                                product.Qty,
                                product.Rep,
                                product.SKU,
                                product.IsUrgent,
                                product.TargetDate
                            },
                            transaction
                        );
                        var productId = await connection.QuerySingleAsync<int>(
                            "SELECT @@IDENTITY AS id",
                            transaction: transaction
                        );
                        product.ProductId = productId;
                        var stepSql = @"
                        INSERT INTO tblProductionSteps 
                            (productid, stepnumber, stepdescription, estmin, steptargetdate, EquipmentName, OperatorName)
                        VALUES
                            (@ProductId, @StepNumber, @StepDescription, @Estmin, @StepTargetDate, @EquipmentName, @OperatorName)";
                        foreach (var step in product.ProductionSteps)
                        {
                            step.ProductId = productId;
                            await connection.ExecuteAsync(
                                stepSql,
                                new
                                {
                                    step.ProductId,
                                    step.StepNumber,
                                    step.StepDescription,
                                    step.Estmin,
                                    step.StepTargetDate,
                                    step.EquipmentName,
                                    step.OperatorName
                                },
                                transaction
                            );
                        }
                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        public List<Product> GetAllProducts()
        {
            using (IDbConnection connection = new OleDbConnection(connectionString))
            {
                var products = connection.Query<Product>(
                    "SELECT * FROM tblProducts").AsList();
                foreach (var product in products)
                {
                    product.ProductionSteps = connection.Query<ProductionStep>(
                        "SELECT * FROM tblProductionSteps WHERE ProductId = @ProductId",
                        new { product.ProductId }).AsList();
                }
                return products;
            }
        }
        public async Task<List<Product>> GetAllProductsAsync()
        {
            using (IDbConnection connection = new OleDbConnection(connectionString))
            {
                var products = (await connection.QueryAsync<Product>(
                    "SELECT * FROM tblProducts")).AsList();
                foreach (var product in products)
                {
                    product.ProductionSteps = (await connection.QueryAsync<ProductionStep>(
                        "SELECT * FROM tblProductionSteps WHERE ProductId = @ProductId",
                        new { product.ProductId })).AsList();
                }
                return products;
            }
        }
        public void UpdateTimeSpent(ProductionStep productionStep)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = "UPDATE tblProductionSteps SET TimeSpent = @TimeSpent WHERE StepID = @StepID";
                var affectedRows = connection.Execute(sql, new { productionStep.TimeSpent, productionStep.StepId });
                if (affectedRows > 0)
                {
                }
                else
                {
                    MessageBox.Show("No record was updated.");
                }
            }
        }
        public async Task UpdateTimeSpentAsync(ProductionStep productionStep)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = "UPDATE tblProductionSteps SET TimeSpent = @TimeSpent WHERE StepID = @StepID";
                var affectedRows = await connection.ExecuteAsync(sql, new { productionStep.TimeSpent, productionStep.StepId });
                if (affectedRows == 0)
                {
                    MessageBox.Show(
                    "No record was updated.",
                    "Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning
                );
                }
            }
        }

        // Time Log related methods
        public async Task<List<TimeLog>> GetTimeLogsForProductionStepAsync(int productionStepId)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = @"
                    SELECT tl.TimeLogId, tl.ProductionStepId, tl.EmployeeId, tl.LoggedDate,
                           tl.StartTime, tl.EndTime, tl.DurationMinutes, tl.WorkDescription,
                           tl.Status, tl.CreatedAt, e.EmpName as ReporterName
                    FROM tblTimeLogs tl
                    INNER JOIN tblEmployees e ON tl.EmployeeId = e.EmpId
                    WHERE tl.ProductionStepId = @ProductionStepId
                    ORDER BY tl.LoggedDate DESC, tl.CreatedAt DESC";

                var timeLogs = await connection.QueryAsync<TimeLog>(sql, new { ProductionStepId = productionStepId });
                return timeLogs.ToList();
            }
        }

        public async Task<int> AddTimeLogAsync(TimeLog timeLog)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = @"
                    INSERT INTO tblTimeLogs
                        (ProductionStepId, EmployeeId, LoggedDate, StartTime, EndTime, DurationMinutes, WorkDescription, Status, CreatedAt)
                    VALUES
                        (@ProductionStepId, @EmployeeId, @LoggedDate, @StartTime, @EndTime, @DurationMinutes, @WorkDescription, @Status, @CreatedAt)";

                var result = await connection.ExecuteAsync(sql, timeLog);

                // Get the ID of the inserted record
                string getIdSql = "SELECT @@IDENTITY";
                var newId = await connection.QuerySingleAsync<int>(getIdSql);
                return newId;
            }
        }

        public async Task UpdateTimeLogAsync(TimeLog timeLog)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = @"
                    UPDATE tblTimeLogs
                    SET EmployeeId = @EmployeeId, LoggedDate = @LoggedDate, StartTime = @StartTime,
                        EndTime = @EndTime, DurationMinutes = @DurationMinutes, WorkDescription = @WorkDescription,
                        Status = @Status
                    WHERE TimeLogId = @TimeLogId";

                var affectedRows = await connection.ExecuteAsync(sql, timeLog);
                if (affectedRows == 0)
                {
                    MessageBox.Show(
                        "No time log record was updated.",
                        "Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                }
            }
        }

        public async Task DeleteTimeLogAsync(int timeLogId)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                string sql = "DELETE FROM tblTimeLogs WHERE TimeLogId = @TimeLogId";
                var affectedRows = await connection.ExecuteAsync(sql, new { TimeLogId = timeLogId });
                if (affectedRows == 0)
                {
                    MessageBox.Show(
                        "No time log record was deleted.",
                        "Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                }
            }
        }
        public void DeleteProduct(int productId)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string deleteStepsSql = "DELETE FROM tblProductionSteps WHERE ProductId = @ProductId";
                        connection.Execute(deleteStepsSql, new { ProductId = productId }, transaction);
                        string deleteProductSql = "DELETE FROM tblProducts WHERE ProductId = @ProductId";
                        connection.Execute(deleteProductSql, new { ProductId = productId }, transaction);
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("An error occurred while deleting the product and its production steps.", ex);
                    }
                }
            }
        }
        public async Task DeleteProductAsync(int productId)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string deleteStepsSql = "DELETE FROM tblProductionSteps WHERE ProductId = @ProductId";
                        await connection.ExecuteAsync(deleteStepsSql, new { ProductId = productId }, transaction);
                        string deleteProductSql = "DELETE FROM tblProducts WHERE ProductId = @ProductId";
                        await connection.ExecuteAsync(deleteProductSql, new { ProductId = productId }, transaction);
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("An error occurred while deleting the product and its production steps.", ex);
                    }
                }
            }
        }
        public void ProductUpdate(Product product)
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string updateProductSql = @"UPDATE tblProducts 
                                                    SET 
                                                        [JobNumber] = @JobNumber, 
                                                        [ProductName] = @ProductName, 
                                                        [Description] = @Description, 
                                                        [Length] = @Length,
                                                        [Width] = @Width,
                                                        [JobType] = @JobType,
                                                        [Qty] = @Qty,
                                                        [Rep] = @Rep,
                                                        [SKU] = @SKU,
                                                        [IsUrgent] = @IsUrgent,
                                                        [TargetDate] = @TargetDate
                                                    WHERE 
                                                        [ProductId] = @ProductId";
                        connection.Execute(updateProductSql, new
                        {
                            product.JobNumber,
                            product.ProductName,
                            product.Description,
                            product.Length,
                            product.Width,
                            product.JobType,
                            product.Qty,
                            product.Rep,
                            product.SKU,
                            product.IsUrgent,
                            product.TargetDate,
                            product.ProductId,
                        }, transaction);
                        string deleteStepsSql = "DELETE FROM tblProductionSteps WHERE ProductId = @ProductId";
                        connection.Execute(deleteStepsSql, new { ProductId = product.ProductId }, transaction);
                        string insertStepSql = @"
                                    INSERT INTO tblProductionSteps 
                                    (
                                        ProductId, 
                                        StepNumber, 
                                        StepDescription, 
                                        Estmin, 
                                        StepTargetDate, 
                                        EquipmentName, 
                                        OperatorName
                                    ) 
                                    VALUES 
                                    (
                                        @ProductId, 
                                        @StepNumber, 
                                        @StepDescription, 
                                        @Estmin, 
                                        @StepTargetDate, 
                                        @EquipmentName, 
                                        @OperatorName
                                    )";
                        var ProductId = product.ProductId;
                        foreach (var step in product.ProductionSteps)
                        {
                            step.ProductId = ProductId;
                            connection.Execute(insertStepSql, new
                            {
                                step.ProductId,
                                step.StepNumber,
                                step.StepDescription,
                                step.Estmin,
                                step.StepTargetDate,
                                step.EquipmentName,
                                step.OperatorName
                            }, transaction);
                        }
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception(ex.ToString() + "An error occurred while updating the product and its production steps.", ex);
                    }
                }
            }
        }
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                await connection.OpenAsync();
                var operators = await connection.QueryAsync<Employee>(
                    "SELECT * FROM tblEmployees"
                );
                return operators.AsList();
            }
        }
        public static void AddEmployee(Employee newEmployee)
        {
            using (IDbConnection db = new OleDbConnection(connectionString))
            {
                string query = @"
                INSERT INTO tblEmployees (EmpName, EmpEmail, EmpPhone, IsActive)
                VALUES (@EmpName, @EmpEmail, @EmpPhone, @IsActive)";
                db.Execute(query, new { newEmployee.EmpName, newEmployee.EmpEmail, newEmployee.EmpPhone, newEmployee.IsActive });
            }
        }
        public static void UpdateEmployee(Employee updatedEmployee)
        {
            using (IDbConnection db = new OleDbConnection(connectionString))
            {
                string query = @"
                UPDATE tblEmployees
                SET EmpName = @EmpName,
                    EmpEmail = @EmpEmail,
                    EmpPhone = @EmpPhone,
                    IsActive = @IsActive
                WHERE EmpId = @EmpId";
                db.Execute(query, new { updatedEmployee.EmpName, updatedEmployee.EmpEmail, updatedEmployee.EmpPhone, updatedEmployee.IsActive, updatedEmployee.EmpId });
            }
        }
        public static void DeleteEmployee(int EmployeeId)
        {
            using (IDbConnection db = new OleDbConnection(connectionString))
            {
                string query = "DELETE FROM tblEmployees WHERE EmpId = @EmpId";
                db.Execute(query, new { EmpId = EmployeeId });
            }
        }
        public async Task<List<Equipment>> GetAllEquipmentAsync()
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                await connection.OpenAsync();
                var equipment = await connection.QueryAsync<Equipment>(
                    "SELECT EquipmentId, EquipmentName FROM tblEquipments"
                );
                return equipment.AsList();
            }
        }
        public static void AddEquipment(Equipment equipment)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                using (var transaction = dbConnection.BeginTransaction())
                {
                    try
                    {
                        const string insertQuery = "INSERT INTO tblEquipments (EquipmentName) VALUES (@EquipmentName)";
                        dbConnection.Execute(insertQuery, new { equipment.EquipmentName }, transaction: transaction);
                        const string identityQuery = "SELECT @@IDENTITY";
                        var id = dbConnection.QuerySingle<int>(identityQuery, transaction: transaction);
                        equipment.EquipmentId = id;
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("An error occurred while adding the equipment.", ex);
                    }
                }
            }
        }
        public static void UpdateEquipment(Equipment equipment)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                const string query = "UPDATE tblEquipments SET EquipmentName = @EquipmentName WHERE EquipmentId = @EquipmentId";
                var rowsAffected = dbConnection.Execute(query, new
                {
                    equipment.EquipmentName,
                    equipment.EquipmentId
                });
                if (rowsAffected == 0)
                {
                    throw new Exception("No equipment record was updated. Equipment may not exist.");
                }
            }
        }
        public static void DeleteEquipment(int equipmentId)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                const string query = "DELETE FROM tblEquipments WHERE EquipmentId = @EquipmentId";
                var rowsAffected = dbConnection.Execute(query, new { EquipmentId = equipmentId });
                if (rowsAffected == 0)
                {
                    throw new Exception("No equipment record was deleted. Equipment may not exist.");
                }
            }
        }
        public async Task<List<StepType>> GetAllStepTypesAsync()
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                await connection.OpenAsync();
                var types = await connection.QueryAsync<StepType>(
                    "SELECT TypeId, StepDescription FROM tblStepTypes"
                );
                return types.AsList();
            }
        }
        public static void AddStepType(StepType stepType)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                using (var transaction = dbConnection.BeginTransaction())
                {
                    try
                    {
                        const string insertQuery = "INSERT INTO tblStepTypes (StepDescription) VALUES (@StepDescription)";
                        dbConnection.Execute(insertQuery, new { stepType.StepDescription }, transaction: transaction);
                        const string identityQuery = "SELECT @@IDENTITY";
                        var id = dbConnection.QuerySingle<int>(identityQuery, transaction: transaction);
                        stepType.TypeId = id;
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("An error occurred while adding the step type.", ex);
                    }
                }
            }
        }
        public static void UpdateStepType(StepType stepType)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                const string query = "UPDATE tblStepTypes SET StepDescription = @StepDescription WHERE TypeId = @TypeId";
                var rowsAffected = dbConnection.Execute(query, new
                {
                    stepType.StepDescription,
                    stepType.TypeId
                });
                if (rowsAffected == 0)
                {
                    throw new Exception("No step type record was updated. StepType may not exist.");
                }
            }
        }
        public static void DeleteStepType(int typeId)
        {
            using (IDbConnection dbConnection = new OleDbConnection(connectionString))
            {
                dbConnection.Open();
                const string query = "DELETE FROM tblStepTypes WHERE TypeId = @TypeId";
                var rowsAffected = dbConnection.Execute(query, new { TypeId = typeId });
                if (rowsAffected == 0)
                {
                    throw new Exception("No step type record was deleted. StepType may not exist.");
                }
            }
        }
    }
}