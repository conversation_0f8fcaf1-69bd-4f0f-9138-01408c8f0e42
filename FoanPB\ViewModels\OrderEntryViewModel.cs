﻿// Start of file: OrderEntryViewModel.cs
using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Configuration;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
namespace FoanPB.ViewModels
{
    public sealed class OrderEntryViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly SharedDataService _shared;
        private readonly MainViewModel _main;
        private readonly DataAccess _dataAccess;
        private readonly TemplateService _templateService;
        private Product _selectedProduct;
        private ProductionStep _selectedStep;
        private ProductionTemplate _selectedTemplate;
        private string _jobNumber = string.Empty;
        private string _productName = string.Empty;
        private string _description = string.Empty;
        private string _jobType = string.Empty;
        private string _rep = string.Empty;
        private string _sku = string.Empty;
        private string _templateName = string.Empty;
        private int _length;
        private int _width;
        private int _qty;
        private bool _isUrgent;
        private bool _isEditing;
        private DateTime? _targetDate;
        private ObservableCollection<ProductionTemplate> _templates = new();
        private ObservableCollection<ProductionStep> _productionSteps = new();
        private ObservableCollection<StepType> _stepTypes = new();
        private ICollectionView _productsView;
        #endregion
        #region Constructor
        public OrderEntryViewModel(SharedDataService shared, MainViewModel main)
        {
            _shared = shared ?? throw new ArgumentNullException(nameof(shared));
            _main = main ?? throw new ArgumentNullException(nameof(main));
            _dataAccess = new DataAccess();
            _templateService = new TemplateService(
                ConfigurationManager.ConnectionStrings["AccessDbConnection"].ConnectionString);
            InitializeProperties();
            InitializeCollections();
            InitializeCommands();
            ResetFilter();
            _ = LoadTemplatesAsync();
        }
        private void InitializeProperties()
        {
            Products = _shared.Products;
            Employees = _shared.Employees;
            Equipments = _shared.Equipment;
            StepTypes = _shared.StepTypes;
        }
        private void InitializeCollections()
        {
            ProductionSteps = new ObservableCollection<ProductionStep>();
            Templates = new ObservableCollection<ProductionTemplate>();
        }
        #endregion
        #region Public Properties
        public ObservableCollection<Product> Products { get; private set; }
        public ObservableCollection<Employee> Employees { get; private set; }
        public ObservableCollection<Equipment> Equipments { get; private set; }
        public ObservableCollection<ProductionTemplate> Templates
        {
            get => _templates;
            private set => SetProperty(ref _templates, value);
        }
        public ObservableCollection<ProductionStep> ProductionSteps
        {
            get => _productionSteps;
            private set
            {
                UnsubscribeFromSteps(_productionSteps);
                if (SetProperty(ref _productionSteps, value))
                {
                    SubscribeToSteps(_productionSteps);
                    RenumberSteps();
                }
            }
        }
        public ObservableCollection<StepType> StepTypes
        {
            get => _stepTypes;
            private set => SetProperty(ref _stepTypes, value);
        }
        public Product SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }
        public ProductionStep SelectedStep
        {
            get => _selectedStep;
            set => SetProperty(ref _selectedStep, value);
        }
        public ProductionTemplate SelectedTemplate
        {
            get => _selectedTemplate;
            set => SetProperty(ref _selectedTemplate, value);
        }
        public string JobNumber
        {
            get => _jobNumber;
            set => SetProductField(ref _jobNumber, value);
        }
        public string ProductName
        {
            get => _productName;
            set => SetProductField(ref _productName, value);
        }
        public string Description
        {
            get => _description;
            set => SetProductField(ref _description, value);
        }
        public int Length
        {
            get => _length;
            set => SetProductField(ref _length, value);
        }
        public int Width
        {
            get => _width;
            set => SetProductField(ref _width, value);
        }
        public string JobType
        {
            get => _jobType;
            set => SetProductField(ref _jobType, value);
        }
        public int Qty
        {
            get => _qty;
            set => SetProductField(ref _qty, value);
        }
        public string Rep
        {
            get => _rep;
            set => SetProductField(ref _rep, value);
        }
        public string SKU
        {
            get => _sku;
            set => SetProductField(ref _sku, value);
        }
        public bool IsUrgent
        {
            get => _isUrgent;
            set => SetProductField(ref _isUrgent, value);
        }
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => SetProductField(ref _targetDate, value);
        }
        public string TemplateName
        {
            get => _templateName;
            set => SetProperty(ref _templateName, value);
        }
        public bool IsEditing
        {
            get => _isEditing;
            private set
            {
                if (SetProperty(ref _isEditing, value))
                    CommandManager.InvalidateRequerySuggested();
            }
        }
        #endregion
        #region Commands
        public ICommand SaveProductCommand { get; private set; }
        public ICommand AddTemplateCommand { get; private set; }
        public ICommand DeleteTemplateCommand { get; private set; }
        public ICommand DeleteProductCommand { get; private set; }
        public ICommand MoveUpCommand { get; private set; }
        public ICommand MoveDownCommand { get; private set; }
        public ICommand LoadTemplateCommand { get; private set; }
        public ICommand ClearInputFieldsCommand { get; private set; }
        public ICommand DeleteStepCommand { get; private set; }
        public ICommand GoBackCommand { get; private set; }
        private void InitializeCommands()
        {
            SaveProductCommand = new RelayCommand(
                async () => await SaveProductAsync());
            AddTemplateCommand = new RelayCommand(
                ExecuteAddTemplate,
                () => !string.IsNullOrWhiteSpace(TemplateName));
            DeleteTemplateCommand = new RelayCommand(
                async () => await DeleteTemplateAsync(),
                () => SelectedTemplate != null);
            DeleteProductCommand = new RelayCommand(
                async () => await DeleteProductAsync(),
                () => SelectedProduct != null);
            MoveUpCommand = new RelayCommand(
                () => MoveStep(-1),
                () => CanMoveStep(-1));
            MoveDownCommand = new RelayCommand(
                () => MoveStep(1),
                () => CanMoveStep(1));
            LoadTemplateCommand = new RelayCommand(
                ExecuteLoadTemplate,
                () => SelectedTemplate != null);
            ClearInputFieldsCommand = new RelayCommand(ClearInputFields);
            DeleteStepCommand = new RelayCommand<ProductionStep>(
                step => ProductionSteps.Remove(step),
                step => step != null);
            GoBackCommand = new RelayCommand(
                () => _main.UpdateViewCommand.Execute("NewOrder"),
                () => !IsEditing);
        }
        #endregion
        #region Command Execution Methods
        private void ExecuteAddTemplate() => AddTemplate();
        private void ExecuteLoadTemplate() => LoadTemplate();
        #endregion
        #region Public Methods
        public void LoadProductForEditing(Product product)
        {
            SelectedProduct = product;
            PopulateFromProduct(product);
        }
        public void PrepareForNewJob()
        {
            ClearInputFields();
        }
        public void ResetFilter()
        {
            _productsView = CollectionViewSource.GetDefaultView(Products);
            _productsView.Filter = null;
            _productsView.Refresh();
        }
        #endregion
        #region Property Change Helpers
        private bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(storage, value))
                return false;
            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        private bool SetProductField<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            var changed = SetProperty(ref storage, value, propertyName);
            if (changed)
                IsEditing = true;
            return changed;
        }
        #endregion
        #region Collection Event Management
        private void SubscribeToSteps(ObservableCollection<ProductionStep> steps)
        {
            if (steps == null) return;
            steps.CollectionChanged += OnStepsCollectionChanged;
            foreach (var step in steps)
                step.PropertyChanged += OnStepPropertyChanged;
        }
        private void UnsubscribeFromSteps(ObservableCollection<ProductionStep> steps)
        {
            if (steps == null) return;
            steps.CollectionChanged -= OnStepsCollectionChanged;
            foreach (var step in steps)
                step.PropertyChanged -= OnStepPropertyChanged;
        }
        private void OnStepsCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            if (e.OldItems != null)
            {
                foreach (INotifyPropertyChanged step in e.OldItems)
                    step.PropertyChanged -= OnStepPropertyChanged;
            }
            if (e.NewItems != null)
            {
                foreach (INotifyPropertyChanged step in e.NewItems)
                    step.PropertyChanged += OnStepPropertyChanged;
            }
            RenumberSteps();
            IsEditing = true;
        }
        private void OnStepPropertyChanged(object sender, PropertyChangedEventArgs e) => IsEditing = true;
        #endregion
        #region Step Management
        private bool CanMoveStep(int offset)
        {
            if (SelectedStep == null) return false;
            var currentIndex = ProductionSteps.IndexOf(SelectedStep);
            var newIndex = currentIndex + offset;
            return newIndex >= 0 && newIndex < ProductionSteps.Count;
        }
        private void MoveStep(int offset)
        {
            if (!CanMoveStep(offset)) return;
            var currentIndex = ProductionSteps.IndexOf(SelectedStep);
            var newIndex = currentIndex + offset;
            ProductionSteps.Move(currentIndex, newIndex);
            RenumberSteps();
        }
        private void RenumberSteps()
        {
            for (int i = 0; i < ProductionSteps.Count; i++)
                ProductionSteps[i].StepNumber = i + 1;
        }
        #endregion
        #region Product CRUD Operations
        private async Task SaveProductAsync()
        {
            try
            {
                if (IsUpdateOperation())
                    await UpdateExistingProductAsync();
                else
                    await CreateNewProductAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Failed to save product: {ex.Message}");
            }
        }
        private bool IsUpdateOperation() => SelectedProduct?.ProductId > 0;
        private async Task CreateNewProductAsync()
        {
            var product = BuildProductFromFields();
            await _dataAccess.AddProductAsync(product);
            Products.Add(product);
            ShowSuccessMessage("Product saved successfully!");
            ClearInputFields();
        }
        private async Task UpdateExistingProductAsync()
        {
            UpdateProductFromFields(SelectedProduct);
            await Task.Run(() => _dataAccess.ProductUpdate(SelectedProduct));
            ShowSuccessMessage("Product updated successfully!");
            ClearInputFields();
        }
        private async Task DeleteProductAsync()
        {
            if (SelectedProduct == null) return;
            try
            {
                await _dataAccess.DeleteProductAsync(SelectedProduct.ProductId);
                Products.Remove(SelectedProduct);
                ClearInputFields();
                ShowSuccessMessage("Product deleted successfully!");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Failed to delete product: {ex.Message}");
            }
        }
        #endregion
        #region Template Operations
        private void AddTemplate()
        {
            try
            {
                var product = BuildProductFromFields();
                var templateDescription = $"Template for {ProductName}";
                var template = _templateService.AddTemplate(product, TemplateName, templateDescription);
                Templates.Add(template);
                SelectedTemplate = template;
                ShowSuccessMessage("Template saved successfully!");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Failed to save template: {ex.Message}");
            }
        }
        private async Task DeleteTemplateAsync()
        {
            if (SelectedTemplate == null) return;
            var confirmationMessage = $"Delete template '{SelectedTemplate.TemplateName}'?";
            if (!ConfirmDeletion(confirmationMessage)) return;
            try
            {
                await _templateService.DeleteTemplateAsync(SelectedTemplate.TemplateId);
                Templates.Remove(SelectedTemplate);
                SelectedTemplate = null;
                ShowSuccessMessage("Template deleted successfully!");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Failed to delete template: {ex.Message}");
            }
        }
        private void LoadTemplate()
        {
            if (SelectedTemplate == null) return;
            PopulateFromTemplate(SelectedTemplate);
            IsEditing = true;
        }
        private async Task LoadTemplatesAsync()
        {
            try
            {
                var templates = await _templateService.GetAllTemplatesWithStepsAsync();
                foreach (var template in templates)
                    Templates.Add(template);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"Failed to load templates: {ex.Message}");
            }
        }
        #endregion
        #region Data Mapping
        private Product BuildProductFromFields() => new Product
        {
            JobNumber = JobNumber,
            ProductName = ProductName,
            Description = Description,
            Length = Length,
            Width = Width,
            JobType = JobType,
            Qty = Qty,
            Rep = Rep,
            SKU = SKU,
            IsUrgent = IsUrgent,
            TargetDate = TargetDate,
            ProductionSteps = ProductionSteps.ToList()
        };
        private void UpdateProductFromFields(Product product)
        {
            if (product == null) return;
            product.JobNumber = JobNumber;
            product.ProductName = ProductName;
            product.Description = Description;
            product.Length = Length;
            product.Width = Width;
            product.JobType = JobType;
            product.Qty = Qty;
            product.Rep = Rep;
            product.SKU = SKU;
            product.IsUrgent = IsUrgent;
            product.TargetDate = TargetDate;
            product.ProductionSteps = ProductionSteps.ToList();
        }
        private void PopulateFromProduct(Product product)
        {
            if (product == null) return;
            JobNumber = product.JobNumber ?? string.Empty;
            ProductName = product.ProductName ?? string.Empty;
            Description = product.Description ?? string.Empty;
            Length = product.Length;
            Width = product.Width;
            JobType = product.JobType ?? string.Empty;
            Qty = product.Qty;
            Rep = product.Rep ?? string.Empty;
            SKU = product.SKU ?? string.Empty;
            IsUrgent = product.IsUrgent;
            TargetDate = product.TargetDate;
            var steps = product.ProductionSteps ?? new List<ProductionStep>();
            ProductionSteps = new ObservableCollection<ProductionStep>(steps);
            IsEditing = false;
        }
        private void PopulateFromTemplate(ProductionTemplate template)
        {
            ProductName = template.ProductName ?? string.Empty;
            Description = template.Description ?? string.Empty;
            Length = template.Length;
            Width = template.Width;
            SKU = template.SKU ?? string.Empty;
            var steps = CreateProductionStepsFromTemplate(template);
            ProductionSteps = new ObservableCollection<ProductionStep>(steps);
        }
        private List<ProductionStep> CreateProductionStepsFromTemplate(ProductionTemplate template)
        {
            if (template.ProductionStepTemplates == null)
                return new List<ProductionStep>();
            return template.ProductionStepTemplates
                .Select(stepTemplate => new ProductionStep
                {
                    StepNumber = stepTemplate.StepNumber,
                    StepDescription = stepTemplate.StepDescription,
                    EquipmentName = stepTemplate.EquipmentName,
                    Estmin = stepTemplate.Estmin
                })
                .ToList();
        }
        #endregion
        #region UI Operations
        private void ClearInputFields()
        {
            SelectedProduct = null;
            ClearTextFields();
            ClearNumericFields();
            ClearBooleanFields();
            TargetDate = null;
            ProductionSteps.Clear();
            IsEditing = false;
        }
        private void ClearTextFields()
        {
            JobNumber = string.Empty;
            ProductName = string.Empty;
            Description = string.Empty;
            JobType = string.Empty;
            Rep = string.Empty;
            SKU = string.Empty;
        }
        private void ClearNumericFields()
        {
            Length = 0;
            Width = 0;
            Qty = 0;
        }
        private void ClearBooleanFields()
        {
            IsUrgent = false;
        }
        #endregion
        #region Message Helpers
        private static void ShowSuccessMessage(string message) =>
            MessageBox.Show(message, "Success", MessageBoxButton.OK, MessageBoxImage.Information);
        private static void ShowErrorMessage(string message) =>
            MessageBox.Show(message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        private static bool ConfirmDeletion(string message) =>
            MessageBox.Show(message, "Confirm Deletion", MessageBoxButton.YesNo, MessageBoxImage.Question)
            == MessageBoxResult.Yes;
        #endregion
    }
}
// End of file: OrderEntryViewModel.cs