﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                     xmlns:local="clr-namespace:SideDrawer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml" />
        <ResourceDictionary Source="Icons.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Typography -->
    <FontFamily x:Key="GlobalFontFamily">sans-serif</FontFamily>
    <FontWeight x:Key="HeadingWeight">Bold</FontWeight>
    <FontWeight x:Key="BodyWeight">Normal</FontWeight>
    <sys:Double x:Key="HeadingSize">17</sys:Double>
    <sys:Double x:Key="BodySize">13.5</sys:Double>
    <sys:Double x:Key="SubtleTextSize">12</sys:Double>

    <!-- Layout -->
    <Thickness x:Key="ContainerPadding">16</Thickness>
    <Thickness x:Key="BorderWidth">1</Thickness>
    <CornerRadius x:Key="BorderRadius">5</CornerRadius>

    <!-- Base TextBlock Styles -->
    <Style x:Key="HeadingTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource GlobalFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource HeadingSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource HeadingWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource GlobalFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodySize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
    </Style>

    <Style x:Key="SecondaryTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource GlobalFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource SubtleTextSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
    </Style>

    <!-- Button Styles -->
    <Style x:Key="BaseButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource BorderWidth}"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FontFamily" Value="{StaticResource GlobalFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodySize}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource BorderRadius}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ButtonHoverBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ButtonActiveBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                            <!--<Setter Property="Cursor" Value="NotAllowed"/>-->
                            <Setter Property="Cursor" Value="{x:Static Cursors.No}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <Style x:Key="IconButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" Background="Transparent" CornerRadius="16">
                        <Path Fill="{StaticResource SecondaryTextBrush}" 
                              Data="{Binding Path=(local:IconAssist.Icon), RelativeSource={RelativeSource TemplatedParent}}" 
                              Stretch="Uniform" 
                              Width="18" Height="18"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ButtonHoverBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ButtonActiveBackgroundBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- TabControl Style (Underlined) -->
    <Style x:Key="UnderlinedTabItem" TargetType="{x:Type TabItem}">
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="FontSize" Value="{StaticResource BodySize}"/>
        <Setter Property="FontWeight" Value="{StaticResource HeadingWeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabItem}">
                    <Grid>
                        <Border Name="Border" BorderThickness="0,0,0,2" BorderBrush="Transparent" Padding="{TemplateBinding Padding}">
                            <ContentPresenter ContentSource="Header" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="TabControl">
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource UnderlinedTabItem}"/>
    </Style>

    <!-- Form Element Styles -->
    <Style TargetType="DatePicker">
        <Setter Property="Padding" Value="8,5"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <Style TargetType="TextBox">
        <Setter Property="Padding" Value="8,5"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Validation.ErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <StackPanel>
                        <AdornedElementPlaceholder/>
                        <TextBlock Text="{Binding [0].ErrorContent}" Foreground="Red" FontSize="11"/>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>