<UserControl x:Class="FoanPB.Views.TimeLogSideDrawer" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:vm="clr-namespace:FoanPB.ViewModels" xmlns:service="clr-namespace:FoanPB.Service" xmlns:controls="clr-namespace:FoanPB.Controls" xmlns:clock="clr-namespace:FoanPB.Controls.Clock" mc:Ignorable="d" d:DataContext="{d:DesignInstance vm:TimeLogSideDrawerViewModel, IsDesignTimeCreatable=True}" Background="White">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <service:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter" />
        <Style x:Key="DrawerTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#333333" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>
        <Style x:Key="DrawerLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="#666666" />
        </Style>
        <Style x:Key="DrawerHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontSize" Value="18" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="#2C3E50" />
        </Style>
        <Style x:Key="DrawerButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#BDC3C7" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#2C3E50" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#ECF0F1" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D5DBDB" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="PrimaryDrawerButtonStyle" TargetType="Button" BasedOn="{StaticResource DrawerButtonStyle}">
            <Setter Property="Background" Value="#3498DB" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#3498DB" />
        </Style>
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Width" Value="32" />
            <Setter Property="Height" Value="32" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="#666666" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="16">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F0F0" />
                                <Setter Property="Foreground" Value="#333333" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E0E0E0" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="DrawerComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="32" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#333333" />
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#BDC3C7" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>
    </UserControl.Resources>
    <DockPanel>
        <Border DockPanel.Dock="Top" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0" Content="✕" Command="{Binding CloseCommand}" Style="{StaticResource CloseButtonStyle}" VerticalAlignment="Top" Margin="0,0,12,0" ToolTip="Close" />
                <StackPanel Grid.Column="1">
                    <TextBlock Text="Log Time" Style="{StaticResource DrawerHeaderStyle}" />
                    <TextBlock Text="{Binding StepDisplayText}" Style="{StaticResource DrawerTextBlockStyle}" Margin="0,4,0,0" />
                    <TextBlock Text="{Binding ProductDisplayText}" Style="{StaticResource DrawerLabelStyle}" />
                </StackPanel>
                <StackPanel Grid.Column="2" VerticalAlignment="Top" Margin="12,0,0,0">
                    <TextBlock Text="Status" Style="{StaticResource DrawerLabelStyle}" Margin="0,0,0,4" />
                    <ComboBox ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding StepStatus}" Style="{StaticResource DrawerComboBoxStyle}" Width="100" Margin="0" ToolTip="Set production step status" />
                </StackPanel>
            </Grid>
        </Border>
        <Grid DockPanel.Dock="Bottom">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20" Visibility="{Binding IsFormVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid Grid.IsSharedSizeScope="True">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" SharedSizeGroup="Labels" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="20" />
                        <ColumnDefinition Width="Auto" SharedSizeGroup="Labels" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="4" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Date" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <DatePicker Grid.Row="0" Grid.Column="1" SelectedDate="{Binding SelectedDate}" Height="32" />
                    <TextBlock Grid.Row="0" Grid.Column="3" Text="Reporter" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <ComboBox Grid.Row="0" Grid.Column="4" ItemsSource="{Binding AvailableReporters}" SelectedItem="{Binding SelectedReporter}" DisplayMemberPath="EmpName" Style="{StaticResource DrawerComboBoxStyle}" Margin="0" ToolTip="Select the employee reporting this time" />
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Start Time" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <controls:TimePicker Grid.Row="2" Grid.Column="1" SelectedTime="{Binding StartTimeAsDateTime}" ToolTip="Select start time" Height="32">
                        <controls:TimePicker.Clock>
                            <clock:ListClock />
                        </controls:TimePicker.Clock>
                    </controls:TimePicker>
                    <TextBlock Grid.Row="2" Grid.Column="3" Text="End Time" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <controls:TimePicker Grid.Row="2" Grid.Column="4" SelectedTime="{Binding EndTimeAsDateTime}" ToolTip="Select end time" Height="32">
                        <controls:TimePicker.Clock>
                            <clock:ListClock />
                        </controls:TimePicker.Clock>
                    </controls:TimePicker>
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Duration (Minutes)" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <Border Grid.Row="4" Grid.Column="1" BorderBrush="#BDC3C7" BorderThickness="1" Background="#F8F9FA" CornerRadius="2" Height="32">
                        <TextBlock Text="{Binding ManualMinutes}" VerticalAlignment="Center" Padding="8,0,0,0" Foreground="#495057" FontWeight="SemiBold" ToolTip="Automatically calculated from start and end time" />
                    </Border>
                    <TextBlock Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="5" Text="Work Description (Optional)" Style="{StaticResource DrawerLabelStyle}" />
                    <TextBox Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="5" Text="{Binding WorkDescription}" TextWrapping="Wrap" AcceptsReturn="True" MinHeight="80" VerticalContentAlignment="Top" ToolTip="Describe the work performed during this time" />
                    <StackPanel Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="Cancel" Command="{Binding CancelCommand}" Style="{StaticResource DrawerButtonStyle}" Margin="0,0,8,0" ToolTip="Cancel and clear the form" />
                        <Button Content="{Binding SaveButtonText}" Command="{Binding SaveTimeLogCommand}" Style="{StaticResource PrimaryDrawerButtonStyle}" ToolTip="Save this time log entry" />
                    </StackPanel>
                </Grid>
            </ScrollViewer>
            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0" Padding="20" Visibility="{Binding IsFormVisible, Converter={StaticResource InvertedBooleanToVisibilityConverter}}">
                <Button Content="Add a new time log" Command="{Binding AddNewTimeLogCommand}" Style="{StaticResource PrimaryDrawerButtonStyle}" ToolTip="Clear form to add another time log entry" />
            </Border>
        </Grid>
        <Grid Margin="20,16,20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <TextBlock Grid.Row="0" Text="{Binding TotalTimeLogged}" Style="{StaticResource DrawerHeaderStyle}" Foreground="#27AE60" FontSize="16" Margin="0,0,0,12" />
            <ListView Grid.Row="2" ItemsSource="{Binding ExistingTimeLogs}" ScrollViewer.VerticalScrollBarVisibility="Auto" BorderBrush="#E9ECEF" BorderThickness="1">
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="Reporter" Width="120">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ReporterName}" Style="{StaticResource DrawerTextBlockStyle}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Duration" Width="80">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding DurationFormatted}" Style="{StaticResource DrawerTextBlockStyle}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Date" Width="100">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding LoggedDateFormatted}" Style="{StaticResource DrawerTextBlockStyle}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Edit" Width="60">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="Edit" Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.EditTimeLogCommand}" CommandParameter="{Binding}" Style="{StaticResource DrawerButtonStyle}" Padding="8,4" FontSize="12" ToolTip="Edit this time log entry" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Delete" Width="70">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="Delete" Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.DeleteTimeLogCommand}" CommandParameter="{Binding}" Style="{StaticResource DrawerButtonStyle}" Padding="8,4" FontSize="12" Background="#E74C3C" Foreground="White" BorderBrush="#C0392B" ToolTip="Delete this time log entry" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
                <ListView.Style>
                    <Style TargetType="ListView">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ExistingTimeLogs.Count}" Value="0">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate>
                                            <Border BorderBrush="#E9ECEF" BorderThickness="1" Padding="20">
                                                <TextBlock Text="No time log entries found for this production step." Style="{StaticResource DrawerTextBlockStyle}" Foreground="#95A5A6" HorizontalAlignment="Center" FontStyle="Italic" />
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ListView.Style>
            </ListView>
        </Grid>
    </DockPanel>
</UserControl>