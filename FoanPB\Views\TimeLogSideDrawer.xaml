<UserControl x:Class="FoanPB.Views.TimeLogSideDrawer" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:vm="clr-namespace:FoanPB.ViewModels" xmlns:service="clr-namespace:FoanPB.Service" xmlns:controls="clr-namespace:FoanPB.Controls" xmlns:clock="clr-namespace:FoanPB.Controls.Clock" mc:Ignorable="d" d:DataContext="{d:DesignInstance vm:TimeLogSideDrawerViewModel, IsDesignTimeCreatable=True}" Background="White">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <service:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter" />
        <service:InvertedBooleanConverter x:Key="InvertedBooleanConverter" />
        <Style x:Key="DrawerTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#333333" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>
        <Style x:Key="DrawerLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="#666666" />
        </Style>
        <Style x:Key="DrawerHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontSize" Value="18" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="#2C3E50" />
        </Style>
        <Style x:Key="DrawerButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#BDC3C7" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#2C3E50" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#ECF0F1" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D5DBDB" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#E0E0E0" />
                                <Setter Property="Foreground" Value="#9E9E9E" />
                                <Setter Property="Cursor" Value="Arrow" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="PrimaryDrawerButtonStyle" TargetType="Button" BasedOn="{StaticResource DrawerButtonStyle}">
            <Setter Property="Background" Value="#3498DB" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#3498DB" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#2471A3" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#A9CCE3" />
                                <Setter Property="Foreground" Value="White" />
                                <Setter Property="BorderBrush" Value="#A9CCE3" />
                                <Setter Property="Cursor" Value="Arrow" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Width" Value="32" />
            <Setter Property="Height" Value="32" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="#666666" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="16">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F0F0" />
                                <Setter Property="Foreground" Value="#333333" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E0E0E0" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="DrawerComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="32" />
            <Setter Property="FontFamily" Value="Segoe UI" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#333333" />
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#BDC3C7" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>
        <Style x:Key="BaseValidationStyle" TargetType="Control">
            <Setter Property="Validation.ErrorTemplate">
                <Setter.Value>
                    <ControlTemplate>
                        <AdornedElementPlaceholder />
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="true">
                    <Setter Property="BorderBrush" Value="Red" />
                    <Setter Property="ToolTip" Value="{Binding RelativeSource={x:Static RelativeSource.Self}, Path=(Validation.Errors)[0].ErrorContent}" />
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="DatePicker" BasedOn="{StaticResource BaseValidationStyle}" />
        <Style TargetType="controls:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="true">
                    <Setter Property="BorderBrush" Value="Red" />
                    <Setter Property="ToolTip" Value="{Binding RelativeSource={x:Static RelativeSource.Self}, Path=(Validation.Errors)[0].ErrorContent}" />
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="ComboBox" BasedOn="{StaticResource DrawerComboBoxStyle}">
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="true">
                    <Setter Property="BorderBrush" Value="Red" />
                    <Setter Property="ToolTip" Value="{Binding RelativeSource={x:Static RelativeSource.Self}, Path=(Validation.Errors)[0].ErrorContent}" />
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <DockPanel>
        <Border DockPanel.Dock="Top" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0" Content="✕" Command="{Binding CloseCommand}" Style="{StaticResource CloseButtonStyle}" VerticalAlignment="Top" Margin="0,0,12,0" ToolTip="Close" />
                <StackPanel Grid.Column="1">
                    <TextBlock Text="Log Time" Style="{StaticResource DrawerHeaderStyle}" />
                    <TextBlock Text="{Binding StepDisplayText}" Style="{StaticResource DrawerTextBlockStyle}" Margin="0,4,0,0" />
                    <TextBlock Text="{Binding ProductDisplayText}" Style="{StaticResource DrawerLabelStyle}" />
                </StackPanel>
                <StackPanel Grid.Column="2" VerticalAlignment="Top" Margin="12,0,0,0">
                    <TextBlock Text="Status" Style="{StaticResource DrawerLabelStyle}" Margin="0,0,0,4" />
                    <ComboBox ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding StepStatus}" Width="100" Margin="0" ToolTip="Set production step status" />
                </StackPanel>
            </Grid>
        </Border>
        <Grid DockPanel.Dock="Bottom">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20" Visibility="{Binding IsFormVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid Grid.IsSharedSizeScope="True">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" SharedSizeGroup="Labels" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="20" />
                        <ColumnDefinition Width="Auto" SharedSizeGroup="Labels" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="4" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="16" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Date" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <DatePicker Grid.Row="0" Grid.Column="1" SelectedDate="{Binding SelectedDate, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" Height="32" />
                    <TextBlock Grid.Row="0" Grid.Column="3" Text="Reporter" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <ComboBox Grid.Row="0" Grid.Column="4" ItemsSource="{Binding AvailableReporters}" SelectedItem="{Binding SelectedReporter, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" DisplayMemberPath="EmpName" Margin="0" ToolTip="Select the employee reporting this time" />
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Start Time" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <controls:TimePicker Grid.Row="2" Grid.Column="1" SelectedTime="{Binding StartTimeAsDateTime, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" ToolTip="Select start time" Height="32">
                        <controls:TimePicker.Clock>
                            <clock:ListClock />
                        </controls:TimePicker.Clock>
                    </controls:TimePicker>
                    <TextBlock Grid.Row="2" Grid.Column="3" Text="End Time" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <controls:TimePicker Grid.Row="2" Grid.Column="4" SelectedTime="{Binding EndTimeAsDateTime, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" ToolTip="Select end time" Height="32">
                        <controls:TimePicker.Clock>
                            <clock:ListClock />
                        </controls:TimePicker.Clock>
                    </controls:TimePicker>
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Duration" Style="{StaticResource DrawerLabelStyle}" VerticalAlignment="Center" Margin="0,0,10,0" />
                    <Border Grid.Row="4" Grid.Column="1" BorderBrush="#BDC3C7" BorderThickness="1" Background="#F8F9FA" CornerRadius="2" Height="32">
                        <TextBlock Text="{Binding ManualDurationFormatted}" VerticalAlignment="Center" Padding="8,0,0,0" Foreground="#495057" FontWeight="SemiBold" ToolTip="Automatically calculated from start and end time" />
                    </Border>
                    <TextBlock Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="5" Text="Work Description" Style="{StaticResource DrawerLabelStyle}" />
                    <TextBox Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="5" Text="{Binding WorkDescription, UpdateSourceTrigger=PropertyChanged}" TextWrapping="Wrap" AcceptsReturn="True" MinHeight="80" VerticalContentAlignment="Top" ToolTip="Describe the work performed during this time (Optional)" />
                    <TextBlock Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="5" Text="{Binding AllErrorsText}" Foreground="Red" TextWrapping="Wrap" Visibility="{Binding HasValidationErrors, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <StackPanel Grid.Row="12" Grid.Column="0" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="Cancel" Command="{Binding CancelCommand}" Style="{StaticResource DrawerButtonStyle}" Margin="0,0,8,0" ToolTip="Cancel and clear the form" />
                        <Button Content="{Binding SaveButtonText}" Command="{Binding SaveTimeLogCommand}" Style="{StaticResource PrimaryDrawerButtonStyle}" ToolTip="Save this time log entry" />
                    </StackPanel>
                </Grid>
            </ScrollViewer>
            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0" Padding="20" Visibility="{Binding IsFormVisible, Converter={StaticResource InvertedBooleanToVisibilityConverter}}">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Add Full Time Log" Command="{Binding AddNewTimeLogCommand}" Style="{StaticResource PrimaryDrawerButtonStyle}" Margin="0,0,8,0" ToolTip="Add a complete time log entry with start and end times" />
                    <Button Content="Start Timer" Command="{Binding StartTimerCommand}" Style="{StaticResource DrawerButtonStyle}" ToolTip="Start a timer - you can complete it later" />
                </StackPanel>
            </Border>
        </Grid>
        <Grid Margin="20,16,20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <TextBlock Grid.Row="0" Text="{Binding TotalTimeLogged}" Style="{StaticResource DrawerHeaderStyle}" Foreground="#27AE60" FontSize="16" Margin="0,0,0,12" />

            <!-- In-Progress Timer Notification -->
            <Border Grid.Row="1" Background="#FFF3CD" BorderBrush="#F39C12" BorderThickness="1" Padding="12" Margin="0,0,0,12" CornerRadius="4" Visibility="{Binding HasInProgressTimer, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⏱️" FontSize="16" Margin="0,0,8,0" />
                    <TextBlock Text="Timer in progress! Click 'Finish Timer' to finish the time log entry." Style="{StaticResource DrawerTextBlockStyle}" Foreground="#856404" FontWeight="SemiBold" />
                </StackPanel>
            </Border>
            <ListView Grid.Row="3" ItemsSource="{Binding ExistingTimeLogs}" ScrollViewer.VerticalScrollBarVisibility="Auto" BorderBrush="#E9ECEF" BorderThickness="1" IsEnabled="{Binding IsEditMode, Converter={StaticResource InvertedBooleanConverter}}">
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="Reporter" Width="100">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ReporterName}" Style="{StaticResource DrawerTextBlockStyle}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Status" Width="80">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding StatusDisplayText}">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsInProgress}" Value="True">
                                                        <Setter Property="Foreground" Value="#F39C12" />
                                                        <Setter Property="FontWeight" Value="SemiBold" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                        <Setter Property="Foreground" Value="#27AE60" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Duration" Width="70">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
                                                <Setter Property="Text" Value="{Binding DurationFormatted}" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsInProgress}" Value="True">
                                                        <Setter Property="Text" Value="--" />
                                                        <Setter Property="Foreground" Value="#95A5A6" />
                                                        <Setter Property="FontStyle" Value="Italic" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Date" Width="100">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding LoggedDateFormatted}" Style="{StaticResource DrawerTextBlockStyle}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Action" Width="90">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Padding="8,4" FontSize="12">
                                        <Button.Style>
                                            <Style TargetType="Button" BasedOn="{StaticResource DrawerButtonStyle}">
                                                <Setter Property="Content" Value="Edit" />
                                                <Setter Property="Command" Value="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.EditTimeLogCommand}" />
                                                <Setter Property="CommandParameter" Value="{Binding}" />
                                                <Setter Property="ToolTip" Value="Edit this time log entry" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsInProgress}" Value="True">
                                                        <Setter Property="Content" Value="Complete" />
                                                        <Setter Property="Command" Value="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.CompleteTimerCommand}" />
                                                        <Setter Property="ToolTip" Value="Complete this timer by adding end time" />
                                                        <Setter Property="Background" Value="#F39C12" />
                                                        <Setter Property="Foreground" Value="White" />
                                                        <Setter Property="BorderBrush" Value="#E67E22" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Delete" Width="70">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="Delete" Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DataContext.DeleteTimeLogCommand}" CommandParameter="{Binding}" Style="{StaticResource DrawerButtonStyle}" Padding="8,4" FontSize="12" Background="#E74C3C" Foreground="White" BorderBrush="#C0392B" ToolTip="Delete this time log entry" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
                <ListView.Style>
                    <Style TargetType="ListView">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ExistingTimeLogs.Count}" Value="0">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate>
                                            <Border BorderBrush="#E9ECEF" BorderThickness="1" Padding="20">
                                                <TextBlock Text="No time log entries found for this production step." Style="{StaticResource DrawerTextBlockStyle}" Foreground="#95A5A6" HorizontalAlignment="Center" FontStyle="Italic" />
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5" />
                                <Setter Property="Background" Value="#F8F9FA" />
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ListView.Style>
            </ListView>
        </Grid>
    </DockPanel>
</UserControl>