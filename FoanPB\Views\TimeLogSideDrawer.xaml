<UserControl x:Class="FoanPB.Views.TimeLogSideDrawer"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:vm="clr-namespace:FoanPB.ViewModels"
             xmlns:service="clr-namespace:FoanPB.Service"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance vm:TimeLogSideDrawerViewModel, IsDesignTimeCreatable=True}"
             Background="White">
    
    <UserControl.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Inverted Boolean to Visibility Converter -->
        <service:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter"/>

        <!-- Time Log Drawer Styles -->
        <Style x:Key="DrawerTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>
        
        <Style x:Key="DrawerLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666666"/>
        </Style>
        
        <Style x:Key="DrawerHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource DrawerTextBlockStyle}">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
        </Style>
        
        <Style x:Key="DrawerButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#ECF0F1"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D5DBDB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="PrimaryDrawerButtonStyle" TargetType="Button" BasedOn="{StaticResource DrawerButtonStyle}">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#3498DB"/>
        </Style>

        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="16">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F0F0"/>
                                <Setter Property="Foreground" Value="#333333"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DrawerComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="25"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
    </UserControl.Resources>

    <DockPanel>
        <!-- Header Section -->
        <Border DockPanel.Dock="Top"
                Background="#F8F9FA"
                BorderBrush="#E9ECEF"
                BorderThickness="0,0,0,1"
                Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Close Button (moved to left) -->
                <Button Grid.Column="0"
                        Content="✕"
                        Command="{Binding CloseCommand}"
                        Style="{StaticResource CloseButtonStyle}"
                        VerticalAlignment="Top"
                        Margin="0,0,12,0"
                        ToolTip="Close"/>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="Log Time" Style="{StaticResource DrawerHeaderStyle}"/>
                    <TextBlock Text="{Binding StepDisplayText}" Style="{StaticResource DrawerTextBlockStyle}" Margin="0,4,0,0"/>
                    <TextBlock Text="{Binding ProductDisplayText}" Style="{StaticResource DrawerLabelStyle}"/>
                </StackPanel>

                <!-- Status Selection -->
                <StackPanel Grid.Column="2" VerticalAlignment="Top" Margin="12,0,0,0">
                    <TextBlock Text="Status" Style="{StaticResource DrawerLabelStyle}" Margin="0,0,0,4"/>
                    <ComboBox ItemsSource="{Binding StatusOptions}"
                              SelectedItem="{Binding SelectedStatus}"
                              Style="{StaticResource DrawerComboBoxStyle}"
                              Width="100"
                              Margin="0"
                              ToolTip="Set production step status"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Footer/Action Section -->
        <Border DockPanel.Dock="Bottom"
                Background="#F8F9FA"
                BorderBrush="#E9ECEF"
                BorderThickness="0,1,0,0"
                Padding="20">
            <!-- Footer Section (visible when form is hidden) -->
            <StackPanel Visibility="{Binding IsFormVisible, Converter={StaticResource InvertedBooleanToVisibilityConverter}}">
                <!-- Add New Time Log Button -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Add a new time log"
                            Command="{Binding AddNewTimeLogCommand}"
                            Style="{StaticResource DrawerButtonStyle}"
                            ToolTip="Clear form to add another time log entry"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Main Content Section -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel Visibility="{Binding IsFormVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <!-- Date and Reporter Selection -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Date Selection -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="Date" Style="{StaticResource DrawerLabelStyle}"/>
                        <DatePicker SelectedDate="{Binding SelectedDate}"
                                   Width="200"
                                   HorizontalAlignment="Left"/>
                    </StackPanel>

                    <!-- Reporter Selection -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="Reporter" Style="{StaticResource DrawerLabelStyle}"/>
                        <ComboBox ItemsSource="{Binding AvailableReporters}"
                                  SelectedItem="{Binding SelectedReporter}"
                                  DisplayMemberPath="EmpName"
                                  Style="{StaticResource DrawerComboBoxStyle}"
                                  Width="200"
                                  HorizontalAlignment="Left"
                                  ToolTip="Select the employee reporting this time"/>
                    </StackPanel>
                </Grid>

                <!-- Time Input Section -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Start Time -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="Start Time" Style="{StaticResource DrawerLabelStyle}"/>
                        <TextBox Text="{Binding StartTime, StringFormat=hh\\:mm}" 
                                 ToolTip="Format: HH:MM (24-hour format)"/>
                    </StackPanel>
                    
                    <!-- End Time -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="End Time" Style="{StaticResource DrawerLabelStyle}"/>
                        <TextBox Text="{Binding EndTime, StringFormat=hh\\:mm}" 
                                 ToolTip="Format: HH:MM (24-hour format)"/>
                    </StackPanel>
                </Grid>

                <!-- Calculated Duration Display -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Duration (Minutes)" Style="{StaticResource DrawerLabelStyle}"/>
                    <TextBox Text="{Binding ManualMinutes}" 
                             IsReadOnly="True" 
                             Background="#F8F9FA"
                             ToolTip="Automatically calculated from start and end time"/>
                </StackPanel>

                <!-- Work Description -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Work Description (Optional)" Style="{StaticResource DrawerLabelStyle}"/>
                    <TextBox Text="{Binding WorkDescription}"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             MinHeight="80"
                             VerticalContentAlignment="Top"
                             ToolTip="Describe the work performed during this time"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="Cancel"
                            Command="{Binding CancelCommand}"
                            Style="{StaticResource DrawerButtonStyle}"
                            Margin="0,0,8,0"
                            ToolTip="Cancel and clear the form"/>
                    <Button Content="Save Time Log"
                            Command="{Binding SaveTimeLogCommand}"
                            Style="{StaticResource PrimaryDrawerButtonStyle}"
                            ToolTip="Save this time log entry"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </DockPanel>
</UserControl>
