﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class DespatchViewModel : StepTypeViewModelBase
    {
        // Target the "Despatch" step for processing
        protected override string TargetStepDescription => "Despatch";

        private string _startTimeText;
        public string StartTimeText
        {
            get => _startTimeText;
            set
            {
                _startTimeText = value;
                OnPropertyChanged(nameof(StartTimeText));
            }
        }

        public ICommand UpdateToCurrentDateCommand { get; }

        public DespatchViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
            // Command to set StartTimeText to now
            UpdateToCurrentDateCommand = new RelayCommand(UpdateToCurrentDate);
        }

        private void UpdateToCurrentDate()
        {
            StartTimeText = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        }

        // Optionally override detail filtering if needed:
        //protected override bool FilterSearchByDetail(object obj)
        //{
        //    // custom filtering logic for Despatch, or defer to base
        //    return base.FilterSearchByDetail(obj);
        //}
    }
}
