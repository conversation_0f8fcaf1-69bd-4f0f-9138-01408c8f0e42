﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using FoanPB.Service;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class OperatorViewModel : BaseViewModel
    {
        #region Private Variables
        private bool _IsCancelEnabled = false;
        private bool _IsSaveEnabled = false;
        private bool _IsAddEnabled = true;
        private bool _IsDetailEnabled = false;
        private bool _IsListEnabled = true;
        
        #endregion

        #region Public Properties
        public bool IsCancelEnabled
        {
            get { return _IsCancelEnabled; }
            set
            {
                _IsCancelEnabled = value;
                OnPropertyChanged("IsCancelEnabled");
            }
        }

        public bool IsListEnabled
        {
            get { return _IsListEnabled; }
            set
            {
                _IsListEnabled = value;
                OnPropertyChanged("IsListEnabled");
            }
        }

        public bool IsDetailEnabled
        {
            get { return _IsDetailEnabled; }
            set
            {
                _IsDetailEnabled = value;
                OnPropertyChanged("IsDetailEnabled");
            }
        }

        public bool IsAddEnabled
        {
            get { return _IsAddEnabled; }
            set
            {
                _IsAddEnabled = value;
                OnPropertyChanged("IsAddEnabled");
            }
        }
        #endregion

        private readonly OperatorService _operatorService;
        private Employee _originalOperator;
        private readonly IDialogService _dialogService;
        public ObservableCollection<Department> OperatorDepartments { get; set; }

        private ObservableCollection<Employee> _operators;

        public ObservableCollection<Employee> Operators
        {
            get => _operators;
            set
            {
                _operators = value;
                OnPropertyChanged(nameof(Operators));
            }
        }

        private Employee _selectedOperator;
        public Employee SelectedOperator
        {
            get => _selectedOperator;
            set
            {
                _selectedOperator = value;
                OnPropertyChanged(nameof(SelectedOperator));
            }
        }


        public bool IsActive
        {
            get => SelectedOperator?.IsActive ?? false;
            set
            {
                if (SelectedOperator != null)
                {
                    SelectedOperator.IsActive = value;
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public ICommand SaveCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand AddCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand UndoCommand { get; }
        public ICommand OpenDepartmentPopupCommand { get; }



        public OperatorViewModel(OperatorService operatorService, IDialogService dialogService)
        {
            _operatorService = operatorService;
            _dialogService = dialogService;
            Operators = new ObservableCollection<Employee>(_operatorService.GetAllOperators());

            OperatorDepartments = new ObservableCollection<Department>(_operatorService.GetOperatorDepartments());

            // Subscribe to the event
            DepartmentMessageCenter.DepartmentsUpdated += OnDepartmentsUpdated;

            SaveCommand = new RelayCommand(SaveOperator);
            DeleteCommand = new RelayCommand<Employee>(DeleteOperator);
            AddCommand = new RelayCommand(AddOperator);
            EditCommand = new RelayCommand<Employee>(EditOperator);
            UndoCommand = new RelayCommand(UndoChanges);
            OpenDepartmentPopupCommand = new RelayCommand(OpenDepartmentPopup);

        }

        

        private void OnDepartmentsUpdated()
        {
            // Refresh the operator departments
            OperatorDepartments = new ObservableCollection<Department>(_operatorService.GetOperatorDepartments());
            OnPropertyChanged(nameof(OperatorDepartments));
            OnPropertyChanged(nameof(SelectedOperator));
        }


        private async Task OpenDepartmentPopup(object arg)
        {
            _dialogService.ShowDepartmentDialog();
        }

        private void EditOperator(Employee operatorToEdit)
        {

            if (operatorToEdit != null)
            {
                _originalOperator = operatorToEdit.Clone();
                SelectedOperator = operatorToEdit;
                IsDetailEnabled = true;
                IsListEnabled = false;
                IsAddEnabled = false;
            }
        }

        private void UndoChanges()
        {
            if (_originalOperator != null)
            {
                SelectedOperator.EmpName = _originalOperator.EmpName;
                SelectedOperator.EmpDepartmentID = _originalOperator.EmpDepartmentID;
                SelectedOperator.EmpEmail = _originalOperator.EmpEmail;
                SelectedOperator.EmpPhone = _originalOperator.EmpPhone;
                SelectedOperator.IsActive = _originalOperator.IsActive;
            }
            else
            {
                SelectedOperator = null;
            }
            IsListEnabled = true;
            IsDetailEnabled = false;
            IsAddEnabled = true;
        }


        private void AddOperator()
        {
            _originalOperator = SelectedOperator?.Clone();
            SelectedOperator = new Employee();
            IsListEnabled = false;
            IsDetailEnabled = true;
            IsActive = true;
            IsAddEnabled = false;
        } 


        private void SaveOperator()
        {
            if (SelectedOperator != null)
            {
                var selectedId = SelectedOperator.EmpId;
                if (SelectedOperator.EmpId == 0)
                {
                    int newId = _operatorService.AddOperator(SelectedOperator);
                    SelectedOperator.EmpId = newId;
                    Operators.Add(SelectedOperator);
                }
                else
                {
                    _operatorService.UpdateOperator(SelectedOperator);
                    LoadOperators();
                    SelectedOperator = Operators.FirstOrDefault(o => o.EmpId == selectedId);
                }
                
            }
            IsDetailEnabled = false;
            IsListEnabled = true;
            IsAddEnabled = true;
        }

        private void LoadOperators()
        {
            var operators = _operatorService.GetAllOperators();
            Operators = new ObservableCollection<Employee>(operators);
        }

        private void DeleteOperator(Employee operatorToDelete)
        {
            if (operatorToDelete != null && operatorToDelete.EmpId != 0)
            {
                _operatorService.DeleteOperator(operatorToDelete.EmpId);
                Operators.Remove(operatorToDelete);
            }
        }
    }
}
