-- Database Migration Script: Move Status from tblTimeLogs to tblProductionSteps
-- This script migrates the Status field from individual TimeLog entries to ProductionStep level
-- Run this script in your Microsoft Access database

-- IMPORTANT: BACKUP YOUR DATABASE BEFORE RUNNING THIS MIGRATION!

-- =====================================================
-- STEP 1: Add StepStatus column to tblProductionSteps
-- =====================================================

-- Add the new StepStatus column to tblProductionSteps table
ALTER TABLE tblProductionSteps ADD COLUMN StepStatus TEXT(50) DEFAULT 'To do';

-- =====================================================
-- STEP 2: Data Migration Strategy
-- =====================================================

-- OPTION A: Set all existing ProductionSteps to default status
-- This is the simplest approach if you want to start fresh
UPDATE tblProductionSteps SET StepStatus = 'To do';

-- OPTION B: Migrate existing Status data from tblTimeLogs (if you want to preserve existing status)
-- This approach takes the most recent Status from TimeLogs for each ProductionStep
-- Uncomment the following section if you want to preserve existing status data:

/*
-- Update ProductionSteps with the most recent Status from their TimeLogs
UPDATE tblProductionSteps 
SET StepStatus = (
    SELECT TOP 1 tl.[Status]
    FROM tblTimeLogs tl 
    WHERE tl.ProductionStepId = tblProductionSteps.StepId 
    ORDER BY tl.CreatedAt DESC
)
WHERE EXISTS (
    SELECT 1 FROM tblTimeLogs tl2 
    WHERE tl2.ProductionStepId = tblProductionSteps.StepId
);

-- For ProductionSteps with no TimeLogs, set default status
UPDATE tblProductionSteps 
SET StepStatus = 'To do' 
WHERE StepStatus IS NULL;
*/

-- OPTION C: Handle conflicting Status values (if multiple TimeLogs have different Status)
-- This approach provides a report of conflicts before migration
-- Uncomment the following section to identify conflicts:

/*
-- Query to identify ProductionSteps with conflicting Status values in their TimeLogs
SELECT 
    ps.StepId,
    ps.StepDescription,
    COUNT(DISTINCT tl.[Status]) as StatusCount,
    GROUP_CONCAT(DISTINCT tl.[Status]) as StatusValues
FROM tblProductionSteps ps
INNER JOIN tblTimeLogs tl ON ps.StepId = tl.ProductionStepId
GROUP BY ps.StepId, ps.StepDescription
HAVING COUNT(DISTINCT tl.[Status]) > 1
ORDER BY ps.StepId;

-- After reviewing conflicts, you can manually set specific ProductionStep statuses:
-- UPDATE tblProductionSteps SET StepStatus = 'Done' WHERE StepId = 1;
-- UPDATE tblProductionSteps SET StepStatus = 'Doing' WHERE StepId = 2;
*/

-- =====================================================
-- STEP 3: Remove Status column from tblTimeLogs
-- =====================================================

-- WARNING: This will permanently remove the Status column and all its data!
-- Make sure you have completed the data migration above before running this step.

-- Remove the Status column from tblTimeLogs table
-- Note: In Microsoft Access, you may need to do this through the Design View
-- as ALTER TABLE DROP COLUMN is not always supported in all Access versions
-- If the following command fails, manually remove the column in Access Design View:

-- ALTER TABLE tblTimeLogs DROP COLUMN [Status];

-- Alternative approach for Access - Create new table without Status column:
/*
-- Create new table structure without Status column
CREATE TABLE tblTimeLogs_New (
    TimeLogId AUTOINCREMENT PRIMARY KEY,
    ProductionStepId LONG NOT NULL,
    EmployeeId LONG NOT NULL,
    LoggedDate DATETIME NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NOT NULL,
    DurationMinutes LONG NOT NULL,
    WorkDescription MEMO,
    CreatedAt DATETIME DEFAULT Now()
);

-- Copy data from old table to new table (excluding Status column)
INSERT INTO tblTimeLogs_New (ProductionStepId, EmployeeId, LoggedDate, StartTime, EndTime, DurationMinutes, WorkDescription, CreatedAt)
SELECT ProductionStepId, EmployeeId, LoggedDate, StartTime, EndTime, DurationMinutes, WorkDescription, CreatedAt
FROM tblTimeLogs;

-- Rename tables (do this manually in Access)
-- 1. Rename tblTimeLogs to tblTimeLogs_Old
-- 2. Rename tblTimeLogs_New to tblTimeLogs
-- 3. Delete tblTimeLogs_Old after verifying data integrity

-- Recreate indexes on the new table
CREATE INDEX IX_TimeLogs_ProductionStepId ON tblTimeLogs (ProductionStepId);
CREATE INDEX IX_TimeLogs_EmployeeId ON tblTimeLogs (EmployeeId);
CREATE INDEX IX_TimeLogs_LoggedDate ON tblTimeLogs (LoggedDate);
*/

-- =====================================================
-- STEP 4: Verification Queries
-- =====================================================

-- Verify the migration was successful
SELECT 'ProductionSteps with StepStatus' as TableInfo, COUNT(*) as RecordCount 
FROM tblProductionSteps 
WHERE StepStatus IS NOT NULL;

SELECT 'Unique StepStatus values' as Info, StepStatus, COUNT(*) as Count
FROM tblProductionSteps 
GROUP BY StepStatus;

-- Verify TimeLogs table structure (Status column should be removed)
-- This query will fail if Status column still exists (which is expected after migration)
-- SELECT [Status] FROM tblTimeLogs LIMIT 1;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- After running this migration:
-- 1. Update your application code to use StepStatus from ProductionSteps instead of Status from TimeLogs
-- 2. Test all functionality thoroughly
-- 3. Consider keeping a backup of the original database until you're confident the migration is successful
