﻿// Start of file: UpdateViewCommand.cs
using FoanPB.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
namespace FoanPB.Commands
{
    public class UpdateViewCommand : ICommand
    {
        private MainViewModel _mainViewModel;
        private readonly IServiceProvider _serviceProvider;
        public UpdateViewCommand(MainViewModel mainViewModel, IServiceProvider serviceProvider)
        {
            _mainViewModel = mainViewModel;
            _serviceProvider = serviceProvider;
        }
        public event EventHandler? CanExecuteChanged;
        public bool CanExecute(object? parameter)
        {
            return true;
        }
        public void Execute(object? parameter)
        {
            if (parameter != null)
            {
                string viewName = parameter.ToString();
                if (viewName == "NewOrder")
                {
                    var orderEntryVM = _serviceProvider.GetRequiredService<OrderEntryViewModel>();
                    // If a job is being edited, stay on or return to OrderEntryView.
                    // Otherwise, go to the job list (NewOrderView).
                    if (orderEntryVM.IsEditing)
                    {
                        _mainViewModel.SelectedViewModel = orderEntryVM;
                    }
                    else
                    {
                        _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<NewOrderViewModel>();
                    }
                }
                else if (viewName == "Cutting")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<CuttingViewModel>();
                }
                else if (viewName == "Operator")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<OperatorViewModel>();
                }
                else if (viewName == "StraightSew")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<StraightSewViewModel>();
                }
                else if (viewName == "Artwork")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<ArtworkViewModel>();
                }
                else if (viewName == "Applique")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<AppliqueViewModel>();
                }
                else if (viewName == "Headings")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<HeadingsViewModel>();
                }
                else if (viewName == "Despatch")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<DespatchViewModel>();
                }
                else if (viewName == "OrderEntry")
                {
                    var orderEntryVM = _serviceProvider.GetRequiredService<OrderEntryViewModel>();
                    orderEntryVM.PrepareForNewJob();
                    _mainViewModel.SelectedViewModel = orderEntryVM;
                }
                else if (viewName == "Statistics")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<StatisticsViewModel>();
                }
                else if (viewName == "Inventory")
                {
                    _mainViewModel.SelectedViewModel = _serviceProvider.GetRequiredService<InventoryViewModel>();
                }
            }
        }
    }
}

// End of file: UpdateViewCommand.cs