using System;
using System.Windows;
using System.Windows.Controls;

namespace FoanPB.Controls.Panels
{
    /// <summary>
    /// A lightweight panel that can be used as a replacement for Grid when row/column functionality is not needed
    /// </summary>
    /// <remarks>
    /// This is a lightweight alternative to Grid when you don't need row and column separation functionality
    /// </remarks>
    public class SimplePanel : Panel
    {
        /// <summary>
        /// Measures the size required for child elements
        /// </summary>
        /// <param name="constraint">The available size constraint</param>
        /// <returns>The desired size</returns>
        protected override Size MeasureOverride(Size constraint)
        {
            var maxSize = new Size();

            foreach (UIElement child in InternalChildren)
            {
                if (child != null)
                {
                    child.Measure(constraint);
                    maxSize.Width = Math.Max(maxSize.Width, child.DesiredSize.Width);
                    maxSize.Height = Math.Max(maxSize.Height, child.DesiredSize.Height);
                }
            }

            return maxSize;
        }

        /// <summary>
        /// Arranges child elements within the panel
        /// </summary>
        /// <param name="arrangeSize">The final size for the panel</param>
        /// <returns>The actual size used</returns>
        protected override Size ArrangeOverride(Size arrangeSize)
        {
            foreach (UIElement child in InternalChildren)
            {
                child?.Arrange(new Rect(arrangeSize));
            }

            return arrangeSize;
        }
    }
}
