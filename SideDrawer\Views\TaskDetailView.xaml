﻿<UserControl x:Class="SideDrawer.Views.TaskDetailView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:SideDrawer"
             xmlns:vm="clr-namespace:SideDrawer.ViewModels" xmlns:controls="clr-namespace:SideDrawer.Controls"
             mc:Ignorable="d" 
             d:DataContext="{d:DesignInstance vm:TaskDetailViewModel, IsDesignTimeCreatable=True}"
             Background="{StaticResource BackgroundBrush}"
             FontFamily="{StaticResource GlobalFontFamily}">
    <DockPanel>
        <!-- ACTION BAR (Bottom) -->
        <Border DockPanel.Dock="Bottom" Padding="{StaticResource ContainerPadding}"
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <Grid>
                <TextBlock Text="{Binding TotalTimeLogged, StringFormat='Total Logged: {0}'}" 
                           Style="{StaticResource SecondaryTextStyle}"
                           VerticalAlignment="Center" HorizontalAlignment="Left"/>
                <Button HorizontalAlignment="Right" Style="{StaticResource BaseButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <Path Fill="{StaticResource SecondaryTextBrush}" Data="{StaticResource ExportIcon}" Width="16" Height="16" Stretch="Uniform" Margin="0,0,8,0"/>
                        <TextBlock Text="Export" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- MAIN CONTENT -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- TASK HEADER -->
            <Grid Grid.Row="0" Margin="{StaticResource ContainerPadding}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0">
                    <TextBlock Text="{Binding TaskTitle}" Style="{StaticResource HeadingTextStyle}" TextWrapping="Wrap"/>
                    <ComboBox ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding SelectedStatus}"
                              BorderThickness="0" Foreground="{StaticResource PrimaryBrush}" FontWeight="SemiBold"/>
                </StackPanel>
                <Button Grid.Column="1" Style="{StaticResource IconButton}" local:IconAssist.Icon="{StaticResource MoreIcon}" VerticalAlignment="Top"/>
            </Grid>

            <!-- TAB CONTROL -->
            <TabControl Grid.Row="1">
                <TabItem Header="TIME LOG">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding TimeLogs}" Margin="0,12,0,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="{StaticResource BorderBrush}" BorderThickness="1"
                                            CornerRadius="{StaticResource BorderRadius}" Padding="12" Margin="16,0,16,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <Ellipse Width="36" Height="36" Fill="{StaticResource PrimaryBrush}" Margin="0,0,12,0">
                                                <Ellipse.OpacityMask>
                                                    <VisualBrush>
                                                        <VisualBrush.Visual>
                                                            <TextBlock Text="{Binding UserInitials}" FontSize="16" FontWeight="Bold"
                                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </VisualBrush.Visual>
                                                    </VisualBrush>
                                                </Ellipse.OpacityMask>
                                            </Ellipse>
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding UserName}" FontWeight="Bold" Style="{StaticResource BodyTextStyle}"/>
                                                <TextBlock Text="{Binding TimeLogged}" Style="{StaticResource SecondaryTextStyle}"/>
                                            </StackPanel>
                                            <StackPanel Grid.Column="2" VerticalAlignment="Center" Orientation="Horizontal">
                                                <TextBlock Text="{Binding Timestamp, StringFormat='MMM dd, yyyy'}" Style="{StaticResource SecondaryTextStyle}" Margin="0,0,16,0"/>
                                                <Button Style="{StaticResource IconButton}" local:IconAssist.Icon="{StaticResource EditIcon}" Width="28" Height="28"/>
                                                <Button Style="{StaticResource IconButton}" local:IconAssist.Icon="{StaticResource DeleteIcon}" Width="28" Height="28"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </TabItem>
                <TabItem Header="LOG TIME">
                    <StackPanel Margin="{StaticResource ContainerPadding}">
                        <!-- FORM -->
                        <TextBlock Text="Date" Style="{StaticResource SecondaryTextStyle}" Margin="0,0,0,4"/>
                        <DatePicker Margin="0,0,0,12"/>

                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="Start Time" Style="{StaticResource SecondaryTextStyle}" Margin="0,0,0,4"/>
                                <controls:TimePicker SelectedTime="{Binding StartTime, Mode=TwoWay}"/>
                                <!--<TextBox Text="09:00 AM"/>-->
                            </StackPanel>
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="End Time" Style="{StaticResource SecondaryTextStyle}" Margin="0,0,0,4"/>
                                <controls:TimePicker SelectedTime="{Binding EndTime, Mode=TwoWay}"/>
                                <!--<TextBox Text="05:30 PM"/>-->
                            </StackPanel>
                        </Grid>

                        <TextBlock Text="Work Description (optional)" Style="{StaticResource SecondaryTextStyle}" Margin="0,0,0,4"/>
                        <TextBox TextWrapping="Wrap" AcceptsReturn="True" MinHeight="100" VerticalContentAlignment="Top" Margin="0,0,0,24"/>

                        <!-- BUTTON BAR -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="Cancel" Command="{Binding CancelCommand}" Style="{StaticResource BaseButtonStyle}" Margin="0,0,8,0"/>
                            <Button Content="Log time" Command="{Binding LogTimeCommand}" Style="{StaticResource PrimaryButtonStyle}"/>
                        </StackPanel>
                    </StackPanel>
                </TabItem>
            </TabControl>
        </Grid>
    </DockPanel>
</UserControl>