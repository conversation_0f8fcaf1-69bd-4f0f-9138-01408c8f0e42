﻿<UserControl x:Class="FoanPB.Views.StatisticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:FoanPB.Views"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <!-- Header -->
            <RowDefinition Height="Auto" />
            <!-- Summary Cards -->
            <RowDefinition Height="Auto" />
            <!-- Step Statistics DataGrid -->
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="📊 Production Statistics" FontSize="24" FontWeight="Bold" VerticalAlignment="Center" />

            <!-- Refresh Button -->
            <Button Grid.Column="1" Content="🔄 Refresh" Height="30" Margin="10,0,0,0"
                    Command="{Binding RefreshCommand}" ToolTip="Refresh statistics from database">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>

        <!-- Summary Cards Section -->
        <Grid Grid.Row="1" Margin="10,0,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- Total Jobs Card -->
            <Border Grid.Column="0" Background="LightBlue" CornerRadius="5" Padding="15" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="📋 Total Jobs" FontWeight="Bold" FontSize="14" Foreground="DarkBlue" />
                    <TextBlock Text="{Binding TotalJobs}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" />
                </StackPanel>
            </Border>

            <!-- Completed Jobs Card -->
            <Border Grid.Column="1" Background="LightGreen" CornerRadius="5" Padding="15" Margin="2.5,0">
                <StackPanel>
                    <TextBlock Text="✅ Completed Jobs" FontWeight="Bold" FontSize="14" Foreground="DarkGreen" />
                    <TextBlock Text="{Binding CompletedJobs}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" />
                </StackPanel>
            </Border>

            <!-- Overall Average Time Card -->
            <Border Grid.Column="2" Background="LightCoral" CornerRadius="5" Padding="15" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="⏱️ Overall Avg Time" FontWeight="Bold" FontSize="14" Foreground="DarkRed" />
                    <TextBlock Text="{Binding OverallAverageTimeFormatted}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" />
                </StackPanel>
            </Border>
        </Grid>

        <!-- Statistics Tabs Section -->
        <TabControl Grid.Row="2" Margin="10">
            <!-- Step Statistics Tab -->
            <TabItem Header="📈 Step Statistics">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- Section Header -->
                    <TextBlock Grid.Row="0" Text="📈 Average Time by Step Type" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />

                    <!-- DataGrid with Progress Bar Overlay -->
                    <Grid Grid.Row="1">
                <DataGrid ItemsSource="{Binding StepStatistics}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="True"
                          CanUserResizeColumns="True"
                          CanUserSortColumns="True"
                          SelectionMode="Single"
                          SelectionUnit="FullRow"
                          GridLinesVisibility="Horizontal"
                          AlternatingRowBackground="LightGray"
                          HeadersVisibility="Column">
                    <DataGrid.Style>
                        <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                    <Setter Property="IsEnabled" Value="False" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Style>

                    <DataGrid.Columns>
                        <!-- Step Description Column -->
                        <DataGridTextColumn Header="🔧 Step Type" Binding="{Binding StepDescription}" Width="150">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="LightSteelBlue" />
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>

                        <!-- Average Time Column -->
                        <DataGridTextColumn Header="⏱️ Average Time" Binding="{Binding AverageTimeSpentFormatted}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="LightSteelBlue" />
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="DarkBlue" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Total Steps Column -->
                        <DataGridTextColumn Header="📊 Total Steps" Binding="{Binding TotalSteps}" Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="LightSteelBlue" />
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Completed Steps Column -->
                        <DataGridTextColumn Header="✅ Completed" Binding="{Binding CompletedSteps}" Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="LightSteelBlue" />
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    <Setter Property="Foreground" Value="DarkGreen" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Completion Percentage Column -->
                        <DataGridTextColumn Header="📈 Completion %" Binding="{Binding CompletionPercentageFormatted}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="LightSteelBlue" />
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Loading Overlay with ProgressBar -->
                <Border Background="#80FFFFFF" Panel.ZIndex="1">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="25" Margin="0,0,0,10" />
                        <TextBlock Text="Loading statistics..." HorizontalAlignment="Center" FontWeight="Bold" />
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </TabItem>

        <!-- Operator Statistics Tab -->
        <TabItem Header="👷 Operator Statistics">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- Section Header -->
                <TextBlock Grid.Row="0" Text="👷 Total Time by Operator" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" />

                <!-- DataGrid with Progress Bar Overlay -->
                <Grid Grid.Row="1">
                    <DataGrid ItemsSource="{Binding OperatorStatistics}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="True"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              SelectionMode="Single"
                              SelectionUnit="FullRow"
                              GridLinesVisibility="Horizontal"
                              AlternatingRowBackground="LightGray"
                              HeadersVisibility="Column">
                        <DataGrid.Style>
                            <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                        <Setter Property="IsEnabled" Value="False" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Style>

                        <DataGrid.Columns>
                            <!-- Operator Name Column -->
                            <DataGridTextColumn Header="👷 Operator Name" Binding="{Binding OperatorName}" Width="150">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Background" Value="LightSteelBlue" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Total Time (Minutes) Column -->
                            <DataGridTextColumn Header="⏱️ Total Time (min)" Binding="{Binding TotalTimeSpentFormatted}" Width="130">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Background" Value="LightSteelBlue" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Foreground" Value="DarkBlue" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Total Time (Hours) Column -->
                            <DataGridTextColumn Header="🕐 Total Time (hrs)" Binding="{Binding TotalTimeSpentHoursFormatted}" Width="130">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Background" Value="LightSteelBlue" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Foreground" Value="DarkGreen" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Steps Completed Column -->
                            <DataGridTextColumn Header="✅ Steps Completed" Binding="{Binding TotalStepsCompleted}" Width="130">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Background" Value="LightSteelBlue" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Average Time per Step Column -->
                            <DataGridTextColumn Header="📊 Avg Time/Step" Binding="{Binding AverageTimePerStepFormatted}" Width="130">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource {x:Type DataGridColumnHeader}}">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Background" Value="LightSteelBlue" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Foreground" Value="DarkRed" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Loading Overlay with ProgressBar -->
                    <Border Background="#80FFFFFF" Panel.ZIndex="1">
                        <Border.Style>
                            <Style TargetType="Border">
                                <Setter Property="Visibility" Value="Collapsed" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" Width="200" Height="25" Margin="0,0,0,10" />
                            <TextBlock Text="Loading statistics..." HorizontalAlignment="Center" FontWeight="Bold" />
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </TabItem>
    </TabControl>
    </Grid>
</UserControl>
