﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using FoanPB.Service;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class DepartmentViewModel : BaseViewModel
    {
        private readonly DepartmentRepository _departmentRepository;
        private Department _selectedDepartment;
        private string _newDepartmentName;

        public ObservableCollection<Department> Departments { get; set; }

        public Department SelectedDepartment
        {
            get => _selectedDepartment;
            set
            {
                _selectedDepartment = value;
                OnPropertyChanged(nameof(SelectedDepartment));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string NewDepartmentName
        {
            get => _newDepartmentName;
            set
            {
                _newDepartmentName = value;
                OnPropertyChanged(nameof(NewDepartmentName));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public ICommand AddCommand { get; }
        public ICommand UpdateCommand { get; }
        public ICommand DeleteCommand { get; }

        public DepartmentViewModel(DepartmentRepository departmentRepository)
        {
            _departmentRepository = departmentRepository;
            LoadDepartments();

            AddCommand = new RelayCommand(AddDepartment, CanAddDepartment);
            UpdateCommand = new RelayCommand(UpdateDepartment, CanModifyDepartment);
            DeleteCommand = new RelayCommand(DeleteDepartment, CanModifyDepartment);
        }

        private void LoadDepartments()
        {
            var departments = _departmentRepository.GetAllDepartments();
            Departments = new ObservableCollection<Department>(departments);
            OnPropertyChanged(nameof(Departments));
            // Notify others that departments have changed
            //DepartmentMessageCenter.RaiseDepartmentsUpdated();
        }


        private void AddDepartment(object parameter)
        {
            if (!string.IsNullOrWhiteSpace(NewDepartmentName))
            {
                var newDepartment = new Department { DepartmentName = NewDepartmentName };
                _departmentRepository.AddDepartment(newDepartment);
                Departments.Add(newDepartment); // Assuming the repository sets the DepartmentID
                NewDepartmentName = string.Empty;
                DepartmentMessageCenter.RaiseDepartmentsUpdated();
            }
        }

        private bool CanAddDepartment(object parameter) => !string.IsNullOrWhiteSpace(NewDepartmentName);

        private void UpdateDepartment(object parameter)
        {
            if (SelectedDepartment != null)
            {
                _departmentRepository.UpdateDepartment(SelectedDepartment);
                DepartmentMessageCenter.RaiseDepartmentsUpdated();
                //LoadDepartments();
            }
        }

        private void DeleteDepartment(object parameter)
        {
            if (SelectedDepartment != null)
            {
                _departmentRepository.DeleteDepartment(SelectedDepartment.DepartmentID);
                Departments.Remove(SelectedDepartment);
                SelectedDepartment = null;
                DepartmentMessageCenter.RaiseDepartmentsUpdated();
                //LoadDepartments();
            }
        }

        private bool CanModifyDepartment(object parameter) => SelectedDepartment != null;
    }
}
