﻿using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows;

namespace FoanPB.Service
{
    public class ProductionStepToVisibilityConverter : IValueConverter
    {
        // Converts ProductionStep instances to Visible and others to Collapsed
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is ProductionStep ? Visibility.Visible : Visibility.Collapsed;
        }

        // Back conversion is not needed
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
