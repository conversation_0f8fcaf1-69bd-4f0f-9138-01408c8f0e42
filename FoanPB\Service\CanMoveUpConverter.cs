﻿using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace FoanPB.Service
{
    public class CanMoveUpConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var productionSteps = (ObservableCollection<ProductionStep>)parameter;
            var selectedStep = value as ProductionStep;
            return selectedStep != null && productionSteps.IndexOf(selectedStep) > 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
