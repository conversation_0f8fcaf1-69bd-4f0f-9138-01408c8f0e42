﻿<UserControl x:Class="FoanPB.Views.ArtworkView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:service="clr-namespace:FoanPB.Service"
             mc:Ignorable="d" d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <service:StatusToButtonVisibilityConverter x:Key="StatusToButtonVisibility" />
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <!-- Header -->
            <RowDefinition Height="Auto" />
            <!-- Search Controls -->
            <RowDefinition Height="Auto" />
            <!-- Job List DataGrid - takes proportional space -->
            <RowDefinition Height="2*" />
            <!-- Process List DataGrid - takes proportional space -->
            <RowDefinition Height="1*" />
        </Grid.RowDefinitions>

        <TextBlock
              Grid.Row="0"
              Text="Artwork"
              FontSize="24"
              FontWeight="Bold"
              Margin="10" />

        <!-- Enhanced Filter Panel -->
        <Expander Grid.Row="1" Header="🎛️ Filters" IsExpanded="False" Margin="5">
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="10" Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- Row 1: Search Filters -->
                    <GroupBox Grid.Row="0" Header="🔍 Search" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="120" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="20" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="120" />
                                <ColumnDefinition Width="120" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Job Number Search -->
                            <TextBlock Grid.Column="0" Text="Job Number:" VerticalAlignment="Center" Margin="0,0,5,0" />
                            <TextBox Grid.Column="1" x:Name="txtJobNumber" Height="25" Margin="0,0,5,0"
                                     Text="{Binding JobNumber, UpdateSourceTrigger=PropertyChanged}"
                                     ToolTip="Enter job number to search" />
                            <Button Grid.Column="2" Content="🔍" Height="25" Width="30" Margin="0,0,5,0"
                                    Command="{Binding SearchByJobNoCommand}" ToolTip="Search by job number">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                                <Setter Property="IsEnabled" Value="False" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>

                            <!-- Job Detail Search -->
                            <TextBlock Grid.Column="4" Text="Search by:" VerticalAlignment="Center" Margin="0,0,5,0" />
                            <ComboBox Grid.Column="5" x:Name="cmbJobDetail" Height="25" Margin="0,0,5,0"
                                      SelectedValue="{Binding SelectedDetailFilter, UpdateSourceTrigger=PropertyChanged}"
                                      SelectedValuePath="Content" IsTextSearchEnabled="True" IsEditable="True">
                                <ComboBoxItem Content="Product Name" />
                                <ComboBoxItem Content="SKU" />
                                <ComboBoxItem Content="Sale Person" />
                                <ComboBoxItem Content="Operator" />
                            </ComboBox>
                            <TextBox Grid.Column="6" x:Name="txtJobDetail" Height="25" Margin="0,0,5,0"
                                     Text="{Binding FilterDetailText, UpdateSourceTrigger=PropertyChanged}"
                                     ToolTip="Enter search text" />
                            <Button Grid.Column="7" Content="🔍" Height="25" Width="30"
                                    Command="{Binding SearchByDetailCommand}" ToolTip="Search by selected field">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                                <Setter Property="IsEnabled" Value="False" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>
                    </GroupBox>

                    <!-- Row 2: Filter Dropdowns -->
                    <GroupBox Grid.Row="1" Header="🎯 Filters" Margin="0,0,0,10">
                        <StackPanel Orientation="Horizontal">
                            <!-- Job Priority Filter -->
                            <TextBlock Text="Priority:" VerticalAlignment="Center" Margin="0,0,5,0" />
                            <ComboBox x:Name="cmbJobPriority" Height="25" Width="140" Margin="0,0,15,0"
                                      SelectedValue="{Binding SelectedJobPriority, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                      SelectedValuePath="Content" ToolTip="Filter by job priority">
                                <ComboBoxItem Content="All Jobs" />
                                <ComboBoxItem Content="Urgent Jobs" />
                                <ComboBoxItem Content="Over Due" />
                                <ComboBoxItem Content="Due Today" />
                                <ComboBoxItem Content="Due This Week" />
                                <ComboBoxItem Content="Due Next Week" />
                                <ComboBoxItem Content="UnCompleted" />
                                <ComboBoxItem Content="Completed" />
                            </ComboBox>

                            <!-- Job Type Filter -->
                            <TextBlock Text="Type:" VerticalAlignment="Center" Margin="0,0,5,0" />
                            <ComboBox x:Name="cmbJobType" Height="25" Width="120" Margin="0,0,15,0"
                                      SelectedValue="{Binding SelectedJobType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                      SelectedValuePath="Content" ToolTip="Filter by job type">
                                <ComboBoxItem Content="All" />
                                <ComboBoxItem Content="Order" />
                                <ComboBoxItem Content="Store" />
                                <ComboBoxItem Content="Both" />
                                <ComboBoxItem Content="UnKnown" />
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- Row 3: Action Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="🗑️ Clear All" Height="25" Width="80" Margin="5,0"
                                Command="{Binding ClearAllFiltersCommand}" ToolTip="Clear all filters" />
                    </StackPanel>
                </Grid>
            </Border>
        </Expander>

        <!-- Job List Section with Progress Bar Overlay -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0" Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- Left side: Job List title and Refresh button -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="Job List" VerticalAlignment="Center" />
                    <!-- Refresh Button -->
                    <Button Content="🔄 Refresh" Height="25" Margin="10,0,0,0" Command="{Binding RefreshCommand}"
                            ToolTip="Refresh data from database">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                        <Setter Property="IsEnabled" Value="False" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- Right side: Job Counters -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <!-- Urgent Jobs Counter -->
                    <Button Background="Orange" Foreground="White" Height="25" Margin="5,0"
                            Command="{Binding ShowUrgentJobsCommand}"
                            ToolTip="Click to show urgent jobs">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🚨 Urgent Jobs (" />
                                <TextBlock Text="{Binding UrgentJobsCount}" />
                                <TextBlock Text=")" />
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <!-- Over Due Jobs Counter -->
                    <Button Background="Red" Foreground="White" Height="25" Margin="5,0"
                            Command="{Binding ShowOverDueJobsCommand}"
                            ToolTip="Click to show overdue jobs">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="⏰ Over Due (" />
                                <TextBlock Text="{Binding OverDueJobsCount}" />
                                <TextBlock Text=")" />
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
            <DataGrid Grid.Row="1" Margin="5"
                      ItemsSource="{Binding ProductsView}"
                      SelectedItem="{Binding SelectedProduct}"
                      AutoGenerateColumns="False" SelectionMode="Single"
                      SelectionUnit="FullRow"
                      CanUserAddRows="False"
                      IsReadOnly="True">
                <DataGrid.Style>
                    <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Style>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Job Number" Binding="{Binding JobNumber}" />
                    <DataGridTextColumn Header="Product Name" Binding="{Binding ProductName}" />
                    <DataGridTextColumn Header="Description" Binding="{Binding Description}"/>
                    <DataGridTextColumn Header="Length" Binding="{Binding Length}" />
                    <DataGridTextColumn Header="Width" Binding="{Binding Width}" />
                    <DataGridTextColumn Header="Target Date" Binding="{Binding TargetDate, StringFormat=d}" />
                    <DataGridTextColumn Header="Job Type" Binding="{Binding JobType}" />
                    <DataGridTextColumn Header="Qty" Binding="{Binding Qty}" />
                    <DataGridTextColumn Header="SKU" Binding="{Binding SKU}" />
                    <DataGridTextColumn Header="Urgent" Binding="{Binding IsUrgent}" />
                    <DataGridTextColumn Header="Rep." Binding="{Binding Rep}"/>
                    <DataGridTextColumn Header="Sub Time Spent" Binding="{Binding SubTimeSpent}"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- Loading Overlay with ProgressBar -->
            <Border Grid.Row="1" Margin="5" Background="#80FFFFFF" Panel.ZIndex="1">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="Visibility" Value="Collapsed" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>
                <ProgressBar HorizontalAlignment="Center" VerticalAlignment="Center" IsIndeterminate="True" Width="200" Height="25" />
            </Border>
        </Grid>

        <!-- Process List Section -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="Process List" Margin="5" />
            <DataGrid Grid.Row="1"
                      x:Name="ProductStepsDataGrid"
                      Margin="5"
                      ItemsSource="{Binding SelectedProduct.ProductionSteps}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserReorderColumns="False"
                      CanUserResizeColumns="False"
                      CanUserResizeRows="False"
                      CanUserSortColumns="False"
                      SelectionMode="Single"
                      SelectionUnit="FullRow"
                      SelectedItem="{Binding SelectedProductionStep}"
                      IsReadOnly="False"
                      CellEditEnding="ProductStepsDataGrid_CellEditEnding">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Step No." Binding="{Binding StepNumber}" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Step Type" Binding="{Binding StepDescription}" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Target Date" Binding="{Binding StepTargetDate, StringFormat=d}" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Equipment" Binding="{Binding EquipmentName}" IsReadOnly="True"/>

                    <DataGridTemplateColumn Header="Operator">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding OperatorName}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <ComboBox ItemsSource="{Binding DataContext.Operators, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                           DisplayMemberPath="EmpName"
                           SelectedValuePath="EmpName"
                           SelectedValue="{Binding OperatorName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding EmpName}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>

                    <DataGridTextColumn Header="Estimated Minutes" Binding="{Binding Estmin}" IsReadOnly="True"/>
                    <DataGridTemplateColumn Header="Time Spent">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding TimeSpent}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <TextBox Text="{Binding TimeSpent, UpdateSourceTrigger=PropertyChanged}">
                                    <TextBox.Style>
                                        <Style TargetType="TextBox">
                                            <Setter Property="IsEnabled" Value="False" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding StepDescription}" Value="Artwork">
                                                    <Setter Property="IsEnabled" Value="True" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBox.Style>
                                </TextBox>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="Start Time" Binding="{Binding StartTime, UpdateSourceTrigger=PropertyChanged}" />
                    <DataGridTemplateColumn Header="Actions">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <!-- collapse the whole panel unless StepType == 'Artwork' -->
                                    <StackPanel.Style>
                                        <Style TargetType="StackPanel">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding StepDescription}" Value="Artwork">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>

                                    <!-- your buttons here; they'll only show when StepType == Artwork -->
                                    <Button Content="Start"
     Command="{Binding DataContext.StartCommand, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
     CommandParameter="{Binding}"
     Visibility="{Binding Status, Converter={StaticResource StatusToButtonVisibility}, ConverterParameter=Start}" />

                                    <Button Content="Time Log"
     Command="{Binding DataContext.TimeLogCommand, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
     CommandParameter="{Binding}" />

                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
