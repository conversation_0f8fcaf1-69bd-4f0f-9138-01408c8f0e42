﻿// Start of file: NewOrderViewModel.cs
using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class NewOrderViewModel : BaseViewModel
    {
        private readonly SharedDataService _sharedDataService;
        private readonly MainViewModel _mainViewModel;
        private readonly OrderEntryViewModel _orderEntryViewModel;
        private Product _selectedProduct;
        private ICollectionView _productsView;
        public ICollectionView ProductsView => _productsView;
        private string _filterJobNumber;
        public string FilterJobNumber
        {
            get => _filterJobNumber;
            set
            {
                if (_filterJobNumber != value)
                {
                    _filterJobNumber = value;
                    OnPropertyChanged(nameof(FilterJobNumber));
                    _productsView?.Refresh();
                }
            }
        }
        private string _filterProductName;
        public string FilterProductName
        {
            get => _filterProductName;
            set
            {
                if (_filterProductName != value)
                {
                    _filterProductName = value;
                    OnPropertyChanged(nameof(FilterProductName));
                    _productsView?.Refresh();
                }
            }
        }
        private string _filterUrgentStatus = "All";
        public string FilterUrgentStatus
        {
            get => _filterUrgentStatus;
            set
            {
                if (_filterUrgentStatus != value)
                {
                    _filterUrgentStatus = value;
                    OnPropertyChanged(nameof(FilterUrgentStatus));
                    _productsView?.Refresh();
                }
            }
        }
        private string _filterRep = "All";
        public string FilterRep
        {
            get => _filterRep;
            set
            {
                if (_filterRep != value)
                {
                    _filterRep = value;
                    OnPropertyChanged(nameof(FilterRep));
                    _productsView?.Refresh();
                }
            }
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged(nameof(IsBusy));
            }
        }

        public ObservableCollection<string> RepList { get; }

        public ObservableCollection<Product> Products => _sharedDataService.Products;
        public Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (_selectedProduct != value)
                {
                    _selectedProduct = value;
                    OnPropertyChanged(nameof(SelectedProduct));
                }
            }
        }
        public ICommand EditProductCommand { get; }
        public ICommand DeleteProductCommand { get; }
        public ICommand ClearFiltersCommand { get; }
        public ICommand RefreshCommand { get; }

        public NewOrderViewModel(SharedDataService sharedDataService, MainViewModel mainViewModel, OrderEntryViewModel orderEntryViewModel)
        {
            _sharedDataService = sharedDataService;
            _mainViewModel = mainViewModel;
            _orderEntryViewModel = orderEntryViewModel;

            RepList = new ObservableCollection<string>();
            LoadRepList();

            _productsView = CollectionViewSource.GetDefaultView(Products);
            _productsView.Filter = ApplyFilter;
            EditProductCommand = new RelayCommand(EditProduct, () => SelectedProduct != null && !IsBusy);
            DeleteProductCommand = new RelayCommand(DeleteProduct, () => SelectedProduct != null && !IsBusy);
            ClearFiltersCommand = new RelayCommand(ClearFilters, () => !IsBusy);
            RefreshCommand = new RelayCommand(RefreshDataAsync, () => !IsBusy);

            // Listen for changes to the SharedDataService collections
            _sharedDataService.PropertyChanged += OnSharedDataServicePropertyChanged;

            // To keep the rep list updated if products change
            _sharedDataService.Products.CollectionChanged += (s, e) => LoadRepList();
        }

        //private async Task RefreshDataAsync()
        //{
        //    IsBusy = true;
        //    try
        //    {
        //        await _sharedDataService.RefreshDataAsync();
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"Failed to refresh data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        //    }
        //    finally
        //    {
        //        IsBusy = false;
        //    }
        //}

        //private async Task RefreshDataAsync()
        //{
        //    IsBusy = true;
        //    try
        //    {
        //        await Task.Run(() => _sharedDataService.RefreshDataAsync())
        //                  .ConfigureAwait(false);   // keep it off the UI thread
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"Failed to refresh data: {ex.Message}",
        //                        "Error",
        //                        MessageBoxButton.OK,
        //                        MessageBoxImage.Error);
        //    }
        //    finally
        //    {
        //        IsBusy = false;
        //    }
        //}

        private async Task RefreshDataAsync()
        {
            IsBusy = true;
            try
            {
                // Refresh data from the shared data service
                // The PropertyChanged event handler will automatically refresh the ProductsView
                await _sharedDataService.RefreshDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to refresh data: {ex.Message}",
                                "Error",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void OnSharedDataServicePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SharedDataService.Products))
            {
                // Products collection has been updated, refresh the view
                RefreshProductsView();
                // Also refresh the rep list since it depends on products
                LoadRepList();
            }
        }

        private void RefreshProductsView()
        {
            // Store current filter state
            var currentFilter = _productsView?.Filter;
            var currentSelectedProduct = SelectedProduct;

            // Recreate the products view with the new collection
            _productsView = CollectionViewSource.GetDefaultView(Products);

            // Restore filter if it existed
            if (currentFilter != null)
            {
                _productsView.Filter = currentFilter;
            }

            // Notify that ProductsView has changed
            OnPropertyChanged(nameof(ProductsView));

            // Clear selection since the old object may no longer be valid
            SelectedProduct = null;
        }


        //public async Task RefreshDataAsync()
        //{
        //    var dataAccess = new DataAccess();
        //    var newProducts = await dataAccess.GetAllProductsAsync();
        //    // ... other data

        //    Application.Current.Dispatcher.Invoke(() =>
        //    {
        //        Products.Clear();
        //        foreach (var p in newProducts) Products.Add(p);
        //        // ... repeat for other collections
        //    });
        //}

        private void LoadRepList()
        {
            var currentSelection = FilterRep;
            var reps = _sharedDataService.Products
                .Select(p => p.Rep)
                .Where(r => !string.IsNullOrWhiteSpace(r))
                .Distinct()
                .OrderBy(r => r)
                .ToList();

            RepList.Clear();
            RepList.Add("All");
            foreach (var rep in reps)
            {
                RepList.Add(rep);
            }

            // Restore selection if it still exists in the new list
            if (RepList.Contains(currentSelection))
            {
                FilterRep = currentSelection;
            }
            else
            {
                FilterRep = "All";
            }
        }

        private void ClearFilters()
        {
            FilterJobNumber = string.Empty;
            FilterProductName = string.Empty;
            FilterRep = "All";
            FilterUrgentStatus = "All";
        }
        private bool ApplyFilter(object item)
        {
            if (item is Product product)
            {
                bool jobNumberMatch = string.IsNullOrWhiteSpace(FilterJobNumber) ||
                                      (product.JobNumber != null && product.JobNumber.IndexOf(FilterJobNumber, StringComparison.OrdinalIgnoreCase) >= 0);
                bool productNameMatch = string.IsNullOrWhiteSpace(FilterProductName) ||
                                        (product.ProductName != null && product.ProductName.IndexOf(FilterProductName, StringComparison.OrdinalIgnoreCase) >= 0);
                bool repMatch = string.IsNullOrWhiteSpace(FilterRep) || FilterRep == "All" ||
                                (product.Rep != null && product.Rep.Equals(FilterRep, StringComparison.OrdinalIgnoreCase));
                bool urgentMatch = FilterUrgentStatus switch
                {
                    "Yes" => product.IsUrgent,
                    "No" => !product.IsUrgent,
                    _ => true
                };
                return jobNumberMatch && productNameMatch && repMatch && urgentMatch;
            }
            return true;
        }
        private void EditProduct()
        {
            if (SelectedProduct != null)
            {
                _orderEntryViewModel.LoadProductForEditing(SelectedProduct);
                _mainViewModel.SelectedViewModel = _orderEntryViewModel;
            }
        }
      
        private async void DeleteProduct()
        {
            if (SelectedProduct != null)
            {
                // Add confirmation dialog
                var result = MessageBox.Show(
                    $"Are you sure you want to delete job '{SelectedProduct.JobNumber}'?",
                    "Confirm Delete",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var dataAccess = new DataAccess();
                    try
                    {
                        await dataAccess.DeleteProductAsync(SelectedProduct.ProductId);
                        _sharedDataService.Products.Remove(SelectedProduct);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to delete job: {ex.Message}",
                                        "Error",
                                        MessageBoxButton.OK,
                                        MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
// End of file: NewOrderViewModel.cs