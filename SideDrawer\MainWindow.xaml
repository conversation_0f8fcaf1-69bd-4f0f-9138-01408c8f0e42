﻿<Window x:Class="SideDrawer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SideDrawer"
        xmlns:views="clr-namespace:SideDrawer.Views"
        xmlns:vm="clr-namespace:SideDrawer.ViewModels"
        mc:Ignorable="d"
        Title="WPF Side Drawer" Height="720" Width="1280"
        WindowStartupLocation="CenterScreen">

    <Window.DataContext>
        <vm:TaskDetailViewModel/>
    </Window.DataContext>

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="#f0f2f5">
        <!-- Main Content Area -->
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="Main Application Content" FontSize="24" Foreground="#444"/>
            <Button Content="Open Task Details" Style="{StaticResource PrimaryButtonStyle}" 
                    Margin="0,20,0,0" Click="ToggleDrawer_Click"/>
        </StackPanel>

        <!-- Side Drawer -->
        <Border x:Name="SideDrawer"
            Width="450"
            HorizontalAlignment="Right"
            BorderBrush="{StaticResource BorderBrush}"
            BorderThickness="1,0,0,0">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="0"
                              BlurRadius="15"
                              Color="Black"
                              Opacity="0.2" />
                </Border.Effect>
                <Border.RenderTransform>
                    <TranslateTransform x:Name="DrawerTransform" X="450" />
                </Border.RenderTransform>

            <views:TaskDetailView DataContext="{Binding}" />
        </Border>

    </Grid>
</Window>