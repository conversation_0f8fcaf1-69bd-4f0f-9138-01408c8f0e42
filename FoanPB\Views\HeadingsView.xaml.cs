﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using FoanPB.DataService;
using FoanPB.Models;

namespace FoanPB.Views
{
    public partial class HeadingsView : UserControl
    {
        public HeadingsView()
        {
            InitializeComponent();
        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                Dispatcher.BeginInvoke(async () =>
                {
                    if (e.Row.Item is ProductionStep edited)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);

                            // Notify that the ProductionStep TimeSpent has changed, which may affect Product.IsCompleted
                            if (this.DataContext is ViewModels.HeadingsViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (System.Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}
