﻿<Application x:Class="FoanPB.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FoanPB"
             xmlns:views="clr-namespace:FoanPB.Views"
             xmlns:viewmodels="clr-namespace:FoanPB.ViewModels"
             >
    <Application.Resources>
        <DataTemplate DataType="{x:Type viewmodels:NewOrderViewModel}">
            <views:NewOrderView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:CuttingViewModel}">
            <views:CuttingView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:StraightSewViewModel}">
            <views:StraightSewView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:OperatorViewModel}">
            <views:OperatorView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:ArtworkViewModel}">
            <views:ArtworkView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:AppliqueViewModel}">
            <views:AppliqueView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:HeadingsViewModel}">
            <views:HeadingsView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:DespatchViewModel}">
            <views:DespatchView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:OrderEntryViewModel}">
            <views:OrderEntryView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:StatisticsViewModel}">
            <views:StatisticsView />
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewmodels:InventoryViewModel}">
            <views:InventoryView />
        </DataTemplate>

        <!-- Global style for DataGrids to improve readability -->
        <Style TargetType="DataGrid">
            <Setter Property="AlternatingRowBackground" Value="#F7F7F7" />
            <Setter Property="AlternationCount" Value="2" />
            <Setter Property="RowBackground" Value="White" />
            <Setter Property="GridLinesVisibility" Value="Horizontal" />
            <Setter Property="VerticalGridLinesBrush" Value="#DDDDDD" />
            <Setter Property="HorizontalGridLinesBrush" Value="#DDDDDD" />
        </Style>
    </Application.Resources>
</Application>
