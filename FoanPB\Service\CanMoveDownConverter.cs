﻿using FoanPB.Models;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Windows.Data;

namespace FoanPB.Service
{
    public class CanMoveDownConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var productionSteps = (ObservableCollection<ProductionStep>)parameter;
            var selectedStep = value as ProductionStep;
            return selectedStep != null && productionSteps.IndexOf(selectedStep) < productionSteps.Count - 1;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
