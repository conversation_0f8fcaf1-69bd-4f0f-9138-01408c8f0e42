using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using FoanPB.Models;
using FoanPB.Commands;
using FoanPB.DataService;

namespace FoanPB.ViewModels
{
    public class TimeLogSideDrawerViewModel : BaseViewModel
    {
        private readonly DataAccess _dataAccess;
        private readonly SharedDataService _sharedDataService;

        private ProductionStep _selectedProductionStep;
        private bool _isVisible;
        private DateTime _selectedDate;
        private TimeSpan _startTime;
        private TimeSpan _endTime;
        private string _workDescription;
        private int _manualMinutes;
        private Employee _selectedReporter;
        private string _selectedStatus;
        private bool _isFormVisible;

        public ProductionStep SelectedProductionStep
        {
            get => _selectedProductionStep;
            set
            {
                _selectedProductionStep = value;
                OnPropertyChanged(nameof(SelectedProductionStep));
                OnPropertyChanged(nameof(StepDisplayText));
                OnPropertyChanged(nameof(ProductDisplayText));
            }
        }

        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                _isVisible = value;
                OnPropertyChanged(nameof(IsVisible));
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                _selectedDate = value;
                OnPropertyChanged(nameof(SelectedDate));
            }
        }

        public TimeSpan StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                UpdateCalculatedMinutes();
            }
        }

        public TimeSpan EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                UpdateCalculatedMinutes();
            }
        }

        public string WorkDescription
        {
            get => _workDescription;
            set
            {
                _workDescription = value;
                OnPropertyChanged(nameof(WorkDescription));
            }
        }

        public int ManualMinutes
        {
            get => _manualMinutes;
            set
            {
                _manualMinutes = value;
                OnPropertyChanged(nameof(ManualMinutes));
            }
        }

        public Employee SelectedReporter
        {
            get => _selectedReporter;
            set
            {
                _selectedReporter = value;
                OnPropertyChanged(nameof(SelectedReporter));
            }
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                _selectedStatus = value;
                OnPropertyChanged(nameof(SelectedStatus));
            }
        }

        public bool IsFormVisible
        {
            get => _isFormVisible;
            set
            {
                _isFormVisible = value;
                OnPropertyChanged(nameof(IsFormVisible));
            }
        }

        // Collections for ComboBoxes
        public ObservableCollection<Employee> AvailableReporters { get; private set; }
        public ObservableCollection<string> StatusOptions { get; private set; }

        // Display properties for UI
        public string StepDisplayText => SelectedProductionStep != null 
            ? $"Step {SelectedProductionStep.StepNumber}: {SelectedProductionStep.StepDescription}"
            : "No step selected";

        public string ProductDisplayText => SelectedProductionStep != null
            ? $"Job Number: {GetJobNumberForStep(SelectedProductionStep)}"
            : "";

        // Commands
        public ICommand SaveTimeLogCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand CloseCommand { get; }
        public ICommand AddNewTimeLogCommand { get; }

        public TimeLogSideDrawerViewModel(DataAccess dataAccess = null, SharedDataService sharedDataService = null)
        {
            _dataAccess = dataAccess ?? new DataAccess();
            _sharedDataService = sharedDataService;

            // Initialize collections
            AvailableReporters = new ObservableCollection<Employee>();
            StatusOptions = new ObservableCollection<string> { "To do", "Doing", "Done" };

            // Initialize with default values
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0); // 9:00 AM
            EndTime = new TimeSpan(17, 0, 0);  // 5:00 PM
            WorkDescription = string.Empty;
            SelectedStatus = "Doing"; // Default status for time logging context
            IsFormVisible = false; // Form is hidden by default

            // Initialize commands
            SaveTimeLogCommand = new RelayCommand(async (p) => await ExecuteSaveTimeLogAsync(), CanExecuteSaveTimeLog);
            CancelCommand = new RelayCommand(ExecuteCancel);
            CloseCommand = new RelayCommand(ExecuteClose);
            AddNewTimeLogCommand = new RelayCommand(ExecuteAddNewTimeLog);

            // Load available reporters
            LoadAvailableReporters();

            UpdateCalculatedMinutes();
        }

        private void UpdateCalculatedMinutes()
        {
            if (EndTime > StartTime)
            {
                _manualMinutes = (int)(EndTime - StartTime).TotalMinutes;
            }
            else
            {
                _manualMinutes = 0;
            }
            OnPropertyChanged(nameof(ManualMinutes));
        }

        private bool CanExecuteSaveTimeLog(object parameter)
        {
            return SelectedProductionStep != null && ManualMinutes > 0;
        }

        private async System.Threading.Tasks.Task ExecuteSaveTimeLogAsync()
        {
            if (SelectedProductionStep == null) return;

            try
            {
                // Update the TimeSpent property
                SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + ManualMinutes;

                // Save to database
                await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

                // Notify shared data service of the change
                _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);

                // Reset form but keep drawer open
                ResetForm();
            }
            catch (Exception ex)
            {
                // TODO: Implement proper error handling/logging
                System.Windows.MessageBox.Show($"Error saving time log: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void ExecuteCancel(object parameter)
        {
            // Reset form but keep drawer open
            ResetForm();
        }

        private void ExecuteClose(object parameter)
        {
            // Close the drawer without saving (same as Cancel)
            IsVisible = false;

            // Reset form
            ResetForm();
        }

        private void ExecuteAddNewTimeLog(object parameter)
        {
            // Show the form controls and clear the form fields to allow entry of a new time log
            // Keep the same ProductionStep context
            IsFormVisible = true;
            ResetForm();
        }

        private void ResetForm()
        {
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0);
            EndTime = new TimeSpan(17, 0, 0);
            WorkDescription = string.Empty;
            SelectedReporter = null;
            SelectedStatus = "Doing"; // Reset to default status
            IsFormVisible = false; // Hide form by default when resetting
            UpdateCalculatedMinutes();
        }

        public void ShowForProductionStep(ProductionStep step)
        {
            SelectedProductionStep = step;
            IsVisible = true;
            ResetForm();
        }

        private string GetJobNumberForStep(ProductionStep step)
        {
            if (step == null) return "Unknown";

            // Find the parent Product by ProductId using SharedDataService
            var parentProduct = _sharedDataService?.Products?.FirstOrDefault(p => p.ProductId == step.ProductId);
            return parentProduct?.JobNumber ?? "Unknown";
        }

        private void LoadAvailableReporters()
        {
            AvailableReporters.Clear();

            if (_sharedDataService?.Employees != null)
            {
                foreach (var employee in _sharedDataService.Employees.Where(e => e.IsActive))
                {
                    AvailableReporters.Add(employee);
                }
            }

            OnPropertyChanged(nameof(AvailableReporters));
        }
    }
}
