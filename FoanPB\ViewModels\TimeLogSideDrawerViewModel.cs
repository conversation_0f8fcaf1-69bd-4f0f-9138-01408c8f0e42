using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using FoanPB.Models;
using FoanPB.Commands;
using FoanPB.DataService;

namespace FoanPB.ViewModels
{
    public class TimeLogSideDrawerViewModel : BaseViewModel, IDataErrorInfo
    {
        private readonly DataAccess _dataAccess;
        private readonly SharedDataService _sharedDataService;
        private ProductionStep _selectedProductionStep;
        private bool _isVisible;
        private DateTime _selectedDate;
        private TimeSpan? _startTime;
        private TimeSpan? _endTime;
        private string _workDescription;
        private int _manualMinutes;
        private Employee _selectedReporter;
        private bool _isFormVisible;
        private bool _isEditMode;
        private TimeLog _currentEditingTimeLog;
        private bool _isSaving;
        private bool _isStartTimerMode;
        private bool _isCompletingTimer;
        private TimeLog _currentInProgressTimeLog;
        private readonly Dictionary<string, string> _validationErrors = new Dictionary<string, string>();
        private bool _showValidationErrors;

        public ProductionStep SelectedProductionStep
        {
            get => _selectedProductionStep;
            set
            {
                _selectedProductionStep = value;
                OnPropertyChanged(nameof(SelectedProductionStep));
                OnPropertyChanged(nameof(StepDisplayText));
                OnPropertyChanged(nameof(ProductDisplayText));
            }
        }

        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                _isVisible = value;
                OnPropertyChanged(nameof(IsVisible));
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                _selectedDate = value;
                OnPropertyChanged(nameof(SelectedDate));
            }
        }

        public TimeSpan? StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                OnPropertyChanged(nameof(StartTimeAsDateTime));
                UpdateCalculatedMinutes();
                OnPropertyChanged(nameof(EndTimeAsDateTime));
            }
        }

        public TimeSpan? EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                OnPropertyChanged(nameof(EndTimeAsDateTime));
                UpdateCalculatedMinutes();
                OnPropertyChanged(nameof(StartTimeAsDateTime));
            }
        }

        public DateTime? StartTimeAsDateTime
        {
            get => _startTime.HasValue ? new DateTime(1899, 12, 30).Add(_startTime.Value) : (DateTime?)null;
            set
            {
                if ((value?.TimeOfDay) != _startTime)
                {
                    StartTime = value?.TimeOfDay;
                }
            }
        }

        public DateTime? EndTimeAsDateTime
        {
            get => _endTime.HasValue ? new DateTime(1899, 12, 30).Add(_endTime.Value) : (DateTime?)null;
            set
            {
                if ((value?.TimeOfDay) != _endTime)
                {
                    EndTime = value?.TimeOfDay;
                }
            }
        }

        public string WorkDescription
        {
            get => _workDescription;
            set
            {
                _workDescription = value;
                OnPropertyChanged(nameof(WorkDescription));
            }
        }

        public int ManualMinutes
        {
            get => _manualMinutes;
            set
            {
                _manualMinutes = value;
                OnPropertyChanged(nameof(ManualMinutes));
            }
        }

        public string ManualDurationFormatted
        {
            get
            {
                if (ManualMinutes <= 0) return "0 minutes";

                var hours = ManualMinutes / 60;
                var minutes = ManualMinutes % 60;

                var parts = new List<string>();

                if (hours > 0)
                {
                    parts.Add(hours == 1 ? "1 hour" : $"{hours} hours");
                }

                if (minutes > 0)
                {
                    parts.Add(minutes == 1 ? "1 minute" : $"{minutes} minutes");
                }

                return string.Join(" ", parts);
            }
        }

        public Employee SelectedReporter
        {
            get => _selectedReporter;
            set
            {
                _selectedReporter = value;
                OnPropertyChanged(nameof(SelectedReporter));
            }
        }

        public string StepStatus
        {
            get => SelectedProductionStep?.StepStatus ?? "To do";
            set
            {
                if (SelectedProductionStep != null && SelectedProductionStep.StepStatus != value)
                {
                    SelectedProductionStep.StepStatus = value;
                    OnPropertyChanged(nameof(StepStatus));
                    _ = SaveStepStatusAsync();
                }
            }
        }

        public bool IsFormVisible
        {
            get => _isFormVisible;
            set
            {
                _isFormVisible = value;
                OnPropertyChanged(nameof(IsFormVisible));
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged(nameof(IsEditMode));
                OnPropertyChanged(nameof(SaveButtonText));
            }
        }

        public TimeLog CurrentEditingTimeLog
        {
            get => _currentEditingTimeLog;
            set
            {
                _currentEditingTimeLog = value;
                OnPropertyChanged(nameof(CurrentEditingTimeLog));
            }
        }

        public bool IsStartTimerMode
        {
            get => _isStartTimerMode;
            set
            {
                _isStartTimerMode = value;
                OnPropertyChanged(nameof(IsStartTimerMode));
                OnPropertyChanged(nameof(SaveButtonText));
                OnPropertyChanged(nameof(IsEndTimeRequired));
            }
        }

        public bool IsCompletingTimer
        {
            get => _isCompletingTimer;
            set
            {
                _isCompletingTimer = value;
                OnPropertyChanged(nameof(IsCompletingTimer));
                OnPropertyChanged(nameof(SaveButtonText));
            }
        }

        public TimeLog CurrentInProgressTimeLog
        {
            get => _currentInProgressTimeLog;
            set
            {
                _currentInProgressTimeLog = value;
                OnPropertyChanged(nameof(CurrentInProgressTimeLog));
                OnPropertyChanged(nameof(HasInProgressTimer));
            }
        }

        public bool HasInProgressTimer => CurrentInProgressTimeLog != null;

        public bool IsEndTimeRequired => !IsStartTimerMode && !HasInProgressTimer;

        public bool IsSaving
        {
            get => _isSaving;
            set
            {
                if (_isSaving != value)
                {
                    _isSaving = value;
                    OnPropertyChanged(nameof(IsSaving));
                    OnPropertyChanged(nameof(SaveButtonText));
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public ObservableCollection<Employee> AvailableReporters { get; private set; }
        public ObservableCollection<string> StatusOptions { get; private set; }
        public ObservableCollection<TimeLog> ExistingTimeLogs { get; private set; }

        public string StepDisplayText => SelectedProductionStep != null
            ? $"Step {SelectedProductionStep.StepNumber}: {SelectedProductionStep.StepDescription}"
            : "No step selected";

        public string ProductDisplayText => SelectedProductionStep != null
            ? $"Job Number: {GetJobNumberForStep(SelectedProductionStep)}"
            : "";

        public string SaveButtonText
        {
            get
            {
                if (IsSaving) return "Saving...";
                if (IsEditMode) return "Update Time Log";
                if (IsStartTimerMode) return "Start Timer";
                if (IsCompletingTimer) return "Complete Timer";
                return "Save Time Log";
            }
        }

        public string TotalTimeLogged
        {
            get
            {
                // Only include completed time logs in the total
                var totalMinutes = ExistingTimeLogs?.Where(tl => tl.IsCompleted).Sum(tl => tl.DurationMinutes) ?? 0;
                if (totalMinutes <= 0) return "Total Logged: 0 minutes";

                var hours = totalMinutes / 60;
                var minutes = totalMinutes % 60;

                var parts = new List<string>();

                if (hours > 0)
                {
                    parts.Add(hours == 1 ? "1 hour" : $"{hours} hours");
                }

                if (minutes > 0)
                {
                    parts.Add(minutes == 1 ? "1 minute" : $"{minutes} minutes");
                }

                return $"Total Logged: {string.Join(" ", parts)}";
            }
        }

        public ICommand SaveTimeLogCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand CloseCommand { get; }
        public ICommand AddNewTimeLogCommand { get; }
        public ICommand EditTimeLogCommand { get; }
        public ICommand DeleteTimeLogCommand { get; }
        public ICommand StartTimerCommand { get; }
        public ICommand CompleteTimerCommand { get; }

        public bool HasValidationErrors => _validationErrors.Any();
        public string AllErrorsText => _showValidationErrors ? string.Join(Environment.NewLine, _validationErrors.Values.Distinct()) : string.Empty;

        public TimeLogSideDrawerViewModel(DataAccess dataAccess = null, SharedDataService sharedDataService = null)
        {
            _dataAccess = dataAccess ?? new DataAccess();
            _sharedDataService = sharedDataService;
            AvailableReporters = new ObservableCollection<Employee>();
            StatusOptions = new ObservableCollection<string> { "To do", "Doing", "Done" };
            ExistingTimeLogs = new ObservableCollection<TimeLog>();
            ExistingTimeLogs.CollectionChanged += (sender, e) => OnPropertyChanged(nameof(TotalTimeLogged));
            _showValidationErrors = false;
            SaveTimeLogCommand = new RelayCommand(async (p) => await ExecuteSaveTimeLogAsync(), CanExecuteSaveTimeLog);
            CancelCommand = new RelayCommand(ExecuteCancel);
            CloseCommand = new RelayCommand(ExecuteClose);
            AddNewTimeLogCommand = new RelayCommand(ExecuteAddNewTimeLog);
            EditTimeLogCommand = new RelayCommand(ExecuteEditTimeLog);
            DeleteTimeLogCommand = new RelayCommand(async (p) => await ExecuteDeleteTimeLogAsync(p));
            StartTimerCommand = new RelayCommand(ExecuteStartTimer);
            CompleteTimerCommand = new RelayCommand(ExecuteCompleteTimer);
            LoadAvailableReporters();
            ResetFormFields();
        }

        private void UpdateCalculatedMinutes()
        {
            if (EndTime.HasValue && StartTime.HasValue && EndTime.Value > StartTime.Value)
            {
                ManualMinutes = (int)(EndTime.Value - StartTime.Value).TotalMinutes;
            }
            else
            {
                ManualMinutes = 0;
            }
            OnPropertyChanged(nameof(ManualDurationFormatted));
        }

        private bool CanExecuteSaveTimeLog(object parameter)
        {
            return !IsSaving;
        }

        private async System.Threading.Tasks.Task ExecuteSaveTimeLogAsync()
        {
            RevalidateAllProperties();
            if (HasValidationErrors)
            {
                _showValidationErrors = true;
                RevalidateAllProperties(); // Force UI to update and show errors
                return;
            }

            if (IsSaving) return;
            IsSaving = true;
            try
            {
                if (IsEditMode && CurrentEditingTimeLog != null)
                {
                    await UpdateExistingTimeLogAsync();
                }
                else if (IsStartTimerMode)
                {
                    await StartNewTimerAsync();
                    // For start timer mode, preserve CurrentInProgressTimeLog to show notification
                    ResetFormAfterStartTimer();
                }
                else if (IsCompletingTimer && CurrentInProgressTimeLog != null)
                {
                    await CompleteInProgressTimerAsync();
                    ResetForm();
                }
                else
                {
                    // For "Add Full Time Log" operations, always create a new complete time log entry
                    // regardless of whether other in-progress timers exist
                    await CreateNewTimeLogAsync();
                    ResetForm();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error saving time log: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsSaving = false;
            }
        }

        private async System.Threading.Tasks.Task CreateNewTimeLogAsync()
        {
            var timeLog = new TimeLog
            {
                ProductionStepId = SelectedProductionStep.StepId,
                EmployeeId = SelectedReporter.EmpId,
                ReporterName = SelectedReporter.EmpName,
                LoggedDate = SelectedDate,
                StartTime = StartTime.Value,
                EndTime = EndTime.Value,
                DurationMinutes = ManualMinutes,
                WorkDescription = WorkDescription ?? string.Empty,
                LogStatus = "Completed",
                CreatedAt = DateTime.Now
            };
            var newTimeLogId = await _dataAccess.AddTimeLogAsync(timeLog);
            timeLog.TimeLogId = newTimeLogId;
            SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + ManualMinutes;
            await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
            ExistingTimeLogs.Insert(0, timeLog);
            _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
        }

        private async System.Threading.Tasks.Task UpdateExistingTimeLogAsync()
        {
            var originalMinutes = CurrentEditingTimeLog.DurationMinutes;
            var newMinutes = ManualMinutes;
            var minutesDifference = newMinutes - originalMinutes;
            CurrentEditingTimeLog.LoggedDate = SelectedDate;
            CurrentEditingTimeLog.StartTime = StartTime.Value;
            CurrentEditingTimeLog.EndTime = EndTime.Value;
            CurrentEditingTimeLog.DurationMinutes = ManualMinutes;
            CurrentEditingTimeLog.WorkDescription = WorkDescription ?? string.Empty;
            CurrentEditingTimeLog.EmployeeId = SelectedReporter.EmpId;
            CurrentEditingTimeLog.ReporterName = SelectedReporter.EmpName;
            await _dataAccess.UpdateTimeLogAsync(CurrentEditingTimeLog);
            SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + minutesDifference;
            await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
            var existingItem = ExistingTimeLogs.FirstOrDefault(tl => tl.TimeLogId == CurrentEditingTimeLog.TimeLogId);
            if (existingItem != null)
            {
                var index = ExistingTimeLogs.IndexOf(existingItem);
                ExistingTimeLogs.RemoveAt(index);
                ExistingTimeLogs.Insert(index, CurrentEditingTimeLog);
            }
            _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
        }

        private async System.Threading.Tasks.Task StartNewTimerAsync()
        {
            var timeLog = new TimeLog
            {
                ProductionStepId = SelectedProductionStep.StepId,
                EmployeeId = SelectedReporter.EmpId,
                ReporterName = SelectedReporter.EmpName,
                LoggedDate = SelectedDate,
                StartTime = StartTime.Value,
                EndTime = null, // No end time for in-progress timer
                DurationMinutes = 0, // No duration yet
                WorkDescription = WorkDescription ?? string.Empty,
                LogStatus = "InProgress",
                CreatedAt = DateTime.Now
            };

            var newTimeLogId = await _dataAccess.AddTimeLogAsync(timeLog);
            timeLog.TimeLogId = newTimeLogId;
            ExistingTimeLogs.Insert(0, timeLog);

            // Set as current in-progress timer to show notification banner
            CurrentInProgressTimeLog = timeLog;

            OnPropertyChanged(nameof(TotalTimeLogged));
        }

        private async System.Threading.Tasks.Task CompleteInProgressTimerAsync()
        {
            var originalMinutes = CurrentInProgressTimeLog.DurationMinutes;
            var newMinutes = ManualMinutes;
            var minutesDifference = newMinutes - originalMinutes;

            // Update the in-progress timer with end time
            CurrentInProgressTimeLog.EndTime = EndTime.Value;
            CurrentInProgressTimeLog.DurationMinutes = ManualMinutes;
            CurrentInProgressTimeLog.WorkDescription = WorkDescription ?? string.Empty;
            CurrentInProgressTimeLog.LogStatus = "Completed";

            await _dataAccess.UpdateTimeLogAsync(CurrentInProgressTimeLog);

            // Update production step time spent
            SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + minutesDifference;
            await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

            // Update the item in the collection
            var existingItem = ExistingTimeLogs.FirstOrDefault(tl => tl.TimeLogId == CurrentInProgressTimeLog.TimeLogId);
            if (existingItem != null)
            {
                var index = ExistingTimeLogs.IndexOf(existingItem);
                ExistingTimeLogs.RemoveAt(index);
                ExistingTimeLogs.Insert(index, CurrentInProgressTimeLog);
            }

            _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);

            // Clear the in-progress timer to hide notification banner
            CurrentInProgressTimeLog = null;

            OnPropertyChanged(nameof(TotalTimeLogged));
        }

        private void ExecuteCancel(object parameter)
        {
            ResetForm();
        }

        private void ExecuteClose(object parameter)
        {
            IsVisible = false;
            ResetForm();
        }

        private void ResetAndHideErrors()
        {
            _showValidationErrors = false;
            _validationErrors.Clear();
            OnPropertyChanged(nameof(HasValidationErrors));
            OnPropertyChanged(nameof(AllErrorsText));
            RevalidateAllProperties(); // Force UI to clear red borders
        }

        private void ExecuteAddNewTimeLog(object parameter)
        {
            ResetFormFields();
            IsEditMode = false;
            IsStartTimerMode = false;
            IsCompletingTimer = false;
            CurrentEditingTimeLog = null;

            // Preserve notification banner if there are still in-progress timers
            if (CurrentInProgressTimeLog == null)
            {
                // Look for any in-progress timer to maintain notification
                var inProgressTimer = ExistingTimeLogs.FirstOrDefault(tl => tl.IsInProgress);
                CurrentInProgressTimeLog = inProgressTimer;
            }

            IsFormVisible = true;
            ResetAndHideErrors();
        }

        private void ExecuteStartTimer(object parameter)
        {
            ResetFormFields();
            IsEditMode = false;
            IsStartTimerMode = true;
            IsCompletingTimer = false;
            CurrentEditingTimeLog = null;

            // Preserve notification banner if there are still in-progress timers
            if (CurrentInProgressTimeLog == null)
            {
                // Look for any in-progress timer to maintain notification
                var inProgressTimer = ExistingTimeLogs.FirstOrDefault(tl => tl.IsInProgress);
                CurrentInProgressTimeLog = inProgressTimer;
            }

            // Auto-populate start time with current time (strip milliseconds for database compatibility)
            var currentTime = DateTime.Now.TimeOfDay;
            StartTime = new TimeSpan(currentTime.Hours, currentTime.Minutes, currentTime.Seconds);

            IsFormVisible = true;
            ResetAndHideErrors();
        }

        private void ExecuteCompleteTimer(object parameter)
        {
            if (parameter is TimeLog inProgressTimeLog)
            {
                // Load the in-progress time log for completion
                CurrentInProgressTimeLog = inProgressTimeLog;
                IsEditMode = false;
                IsStartTimerMode = false;
                IsCompletingTimer = true;

                // Pre-populate form with existing data
                SelectedDate = inProgressTimeLog.LoggedDate;
                StartTime = inProgressTimeLog.StartTime;
                // Auto-populate end time with current time (strip milliseconds for database compatibility)
                var currentTime = DateTime.Now.TimeOfDay;
                EndTime = new TimeSpan(currentTime.Hours, currentTime.Minutes, currentTime.Seconds);
                WorkDescription = inProgressTimeLog.WorkDescription;
                SelectedReporter = AvailableReporters.FirstOrDefault(r => r.EmpId == inProgressTimeLog.EmployeeId);

                IsFormVisible = true;
                ResetAndHideErrors();
            }
        }

        private void ResetFormFields()
        {
            SelectedDate = DateTime.Today;
            StartTime = null;
            EndTime = null;
            WorkDescription = string.Empty;
            SelectedReporter = null;
        }

        private void ResetForm()
        {
            ResetFormFields();
            IsFormVisible = false;
            IsEditMode = false;
            IsStartTimerMode = false;
            IsCompletingTimer = false;
            CurrentEditingTimeLog = null;
            CurrentInProgressTimeLog = null;
            ResetAndHideErrors();
        }

        private void ResetFormAfterStartTimer()
        {
            ResetFormFields();
            IsFormVisible = false;
            IsEditMode = false;
            IsStartTimerMode = false;
            IsCompletingTimer = false;
            CurrentEditingTimeLog = null;
            // Preserve CurrentInProgressTimeLog to keep notification banner visible
            ResetAndHideErrors();
        }

        private void ExecuteEditTimeLog(object parameter)
        {
            if (parameter is TimeLog timeLog)
            {
                IsEditMode = true;
                IsCompletingTimer = false;
                CurrentEditingTimeLog = timeLog;
                SelectedDate = timeLog.LoggedDate;
                StartTime = timeLog.StartTime;
                EndTime = timeLog.EndTime;
                WorkDescription = timeLog.WorkDescription;
                SelectedReporter = AvailableReporters.FirstOrDefault(r => r.EmpId == timeLog.EmployeeId);
                IsFormVisible = true;
                ResetAndHideErrors();
            }
        }

        private async System.Threading.Tasks.Task ExecuteDeleteTimeLogAsync(object parameter)
        {
            if (parameter is TimeLog timeLog)
            {
                try
                {
                    var result = System.Windows.MessageBox.Show(
                        $"Are you sure you want to delete this time log entry?\n\nReporter: {timeLog.ReporterName}\nDate: {timeLog.LoggedDateFormatted}\nDuration: {timeLog.DurationFormatted}",
                        "Confirm Delete",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);
                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        await _dataAccess.DeleteTimeLogAsync(timeLog.TimeLogId);
                        ExistingTimeLogs.Remove(timeLog);

                        // If the deleted timer was the current in-progress timer, find another in-progress timer or clear notification
                        if (CurrentInProgressTimeLog != null && CurrentInProgressTimeLog.TimeLogId == timeLog.TimeLogId)
                        {
                            // Look for another in-progress timer in the remaining list
                            var remainingInProgressTimer = ExistingTimeLogs.FirstOrDefault(tl => tl.IsInProgress);
                            CurrentInProgressTimeLog = remainingInProgressTimer;
                        }

                        var totalMinutes = ExistingTimeLogs.Sum(tl => tl.DurationMinutes);
                        SelectedProductionStep.TimeSpent = totalMinutes;
                        await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
                        _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
                        OnPropertyChanged(nameof(TotalTimeLogged));
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Error deleting time log: {ex.Message}", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        public async void ShowForProductionStep(ProductionStep step)
        {
            SelectedProductionStep = step;
            IsVisible = true;
            ResetForm();
            OnPropertyChanged(nameof(StepStatus));
            await LoadExistingTimeLogsAsync();
        }

        private async System.Threading.Tasks.Task SaveStepStatusAsync()
        {
            if (SelectedProductionStep == null) return;
            try
            {
                await _dataAccess.UpdateStepStatusAsync(SelectedProductionStep);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving step status: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadExistingTimeLogsAsync()
        {
            if (SelectedProductionStep == null) return;
            try
            {
                var timeLogs = await _dataAccess.GetTimeLogsForProductionStepAsync(SelectedProductionStep.StepId);
                ExistingTimeLogs.Clear();

                // Find any in-progress timer for this production step
                CurrentInProgressTimeLog = null;

                foreach (var timeLog in timeLogs)
                {
                    ExistingTimeLogs.Add(timeLog);

                    // Set the current in-progress timer if found
                    if (timeLog.IsInProgress)
                    {
                        CurrentInProgressTimeLog = timeLog;
                    }
                }
                OnPropertyChanged(nameof(TotalTimeLogged));
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading time logs: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private string GetJobNumberForStep(ProductionStep step)
        {
            if (step == null) return "Unknown";
            var parentProduct = _sharedDataService?.Products?.FirstOrDefault(p => p.ProductId == step.ProductId);
            return parentProduct?.JobNumber ?? "Unknown";
        }

        private void LoadAvailableReporters()
        {
            AvailableReporters.Clear();
            if (_sharedDataService?.Employees != null)
            {
                foreach (var employee in _sharedDataService.Employees.Where(e => e.IsActive))
                {
                    AvailableReporters.Add(employee);
                }
            }
            OnPropertyChanged(nameof(AvailableReporters));
        }

        #region IDataErrorInfo Implementation
        private void RevalidateAllProperties()
        {
            OnPropertyChanged(nameof(SelectedDate));
            OnPropertyChanged(nameof(SelectedReporter));
            OnPropertyChanged(nameof(StartTimeAsDateTime));
            OnPropertyChanged(nameof(EndTimeAsDateTime));
            OnPropertyChanged(nameof(AllErrorsText));
        }

        public string Error => null;

        public string this[string columnName]
        {
            get
            {
                string error = string.Empty;
                switch (columnName)
                {
                    case nameof(SelectedDate):
                        if (SelectedDate == default(DateTime))
                            error = "Date is required.";
                        break;
                    case nameof(SelectedReporter):
                        if (SelectedReporter == null)
                            error = "Reporter is required.";
                        break;
                    case nameof(StartTimeAsDateTime):
                    case nameof(StartTime):
                        if (!StartTime.HasValue)
                            error = "Start time is required.";
                        else if (EndTime.HasValue && StartTime.Value >= EndTime.Value)
                            error = "Start time must be earlier than end time.";
                        break;
                    case nameof(EndTimeAsDateTime):
                    case nameof(EndTime):
                        // EndTime is only required when not in start timer mode and not completing an in-progress timer
                        if (IsEndTimeRequired && !EndTime.HasValue)
                            error = "End time is required.";
                        else if (StartTime.HasValue && EndTime.HasValue && StartTime.Value >= EndTime.Value)
                            error = "Start time must be earlier than end time.";
                        break;
                }

                if (string.IsNullOrEmpty(error))
                {
                    _validationErrors.Remove(columnName);
                }
                else
                {
                    _validationErrors[columnName] = error;
                }

                OnPropertyChanged(nameof(HasValidationErrors));
                OnPropertyChanged(nameof(AllErrorsText));

                return _showValidationErrors ? error : string.Empty;
            }
        }
        #endregion
    }
}