using System;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using FoanPB.Models;
using FoanPB.Commands;
using FoanPB.DataService;

namespace FoanPB.ViewModels
{
    public class TimeLogSideDrawerViewModel : BaseViewModel
    {
        private readonly DataAccess _dataAccess;
        private readonly SharedDataService _sharedDataService;

        private ProductionStep _selectedProductionStep;
        private bool _isVisible;
        private DateTime _selectedDate;
        private TimeSpan _startTime;
        private TimeSpan _endTime;
        private string _workDescription;
        private int _manualMinutes;

        public ProductionStep SelectedProductionStep
        {
            get => _selectedProductionStep;
            set
            {
                _selectedProductionStep = value;
                OnPropertyChanged(nameof(SelectedProductionStep));
                OnPropertyChanged(nameof(StepDisplayText));
                OnPropertyChanged(nameof(ProductDisplayText));
            }
        }

        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                _isVisible = value;
                OnPropertyChanged(nameof(IsVisible));
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                _selectedDate = value;
                OnPropertyChanged(nameof(SelectedDate));
            }
        }

        public TimeSpan StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                UpdateCalculatedMinutes();
            }
        }

        public TimeSpan EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                UpdateCalculatedMinutes();
            }
        }

        public string WorkDescription
        {
            get => _workDescription;
            set
            {
                _workDescription = value;
                OnPropertyChanged(nameof(WorkDescription));
            }
        }

        public int ManualMinutes
        {
            get => _manualMinutes;
            set
            {
                _manualMinutes = value;
                OnPropertyChanged(nameof(ManualMinutes));
            }
        }

        // Display properties for UI
        public string StepDisplayText => SelectedProductionStep != null 
            ? $"Step {SelectedProductionStep.StepNumber}: {SelectedProductionStep.StepDescription}"
            : "No step selected";

        public string ProductDisplayText => SelectedProductionStep != null
            ? $"Job Number: {GetJobNumberForStep(SelectedProductionStep)}"
            : "";

        // Commands
        public ICommand SaveTimeLogCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand CloseCommand { get; }
        public ICommand AddNewTimeLogCommand { get; }

        public TimeLogSideDrawerViewModel(DataAccess dataAccess = null, SharedDataService sharedDataService = null)
        {
            _dataAccess = dataAccess ?? new DataAccess();
            _sharedDataService = sharedDataService;

            // Initialize with default values
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0); // 9:00 AM
            EndTime = new TimeSpan(17, 0, 0);  // 5:00 PM
            WorkDescription = string.Empty;

            // Initialize commands
            SaveTimeLogCommand = new RelayCommand(async (p) => await ExecuteSaveTimeLogAsync(), CanExecuteSaveTimeLog);
            CancelCommand = new RelayCommand(ExecuteCancel);
            CloseCommand = new RelayCommand(ExecuteClose);
            AddNewTimeLogCommand = new RelayCommand(ExecuteAddNewTimeLog);

            UpdateCalculatedMinutes();
        }

        private void UpdateCalculatedMinutes()
        {
            if (EndTime > StartTime)
            {
                _manualMinutes = (int)(EndTime - StartTime).TotalMinutes;
            }
            else
            {
                _manualMinutes = 0;
            }
            OnPropertyChanged(nameof(ManualMinutes));
        }

        private bool CanExecuteSaveTimeLog(object parameter)
        {
            return SelectedProductionStep != null && ManualMinutes > 0;
        }

        private async System.Threading.Tasks.Task ExecuteSaveTimeLogAsync()
        {
            if (SelectedProductionStep == null) return;

            try
            {
                // Update the TimeSpent property
                SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + ManualMinutes;

                // Save to database
                await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

                // Notify shared data service of the change
                _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);

                // Close the drawer
                IsVisible = false;

                // Reset form
                ResetForm();
            }
            catch (Exception ex)
            {
                // TODO: Implement proper error handling/logging
                System.Windows.MessageBox.Show($"Error saving time log: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void ExecuteCancel(object parameter)
        {
            // Close the drawer without saving
            IsVisible = false;

            // Reset form
            ResetForm();
        }

        private void ExecuteClose(object parameter)
        {
            // Close the drawer without saving (same as Cancel)
            IsVisible = false;

            // Reset form
            ResetForm();
        }

        private void ExecuteAddNewTimeLog(object parameter)
        {
            // Clear the form fields to allow entry of a new time log
            // Keep the same ProductionStep context
            ResetForm();
        }

        private void ResetForm()
        {
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0);
            EndTime = new TimeSpan(17, 0, 0);
            WorkDescription = string.Empty;
            UpdateCalculatedMinutes();
        }

        public void ShowForProductionStep(ProductionStep step)
        {
            SelectedProductionStep = step;
            IsVisible = true;
            ResetForm();
        }

        private string GetJobNumberForStep(ProductionStep step)
        {
            if (step == null) return "Unknown";

            // Find the parent Product by ProductId using SharedDataService
            var parentProduct = _sharedDataService?.Products?.FirstOrDefault(p => p.ProductId == step.ProductId);
            return parentProduct?.JobNumber ?? "Unknown";
        }
    }
}
