using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using FoanPB.Models;
using FoanPB.Commands;
using FoanPB.DataService;

namespace FoanPB.ViewModels
{
    public class TimeLogSideDrawerViewModel : BaseViewModel
    {
        private readonly DataAccess _dataAccess;
        private readonly SharedDataService _sharedDataService;

        private ProductionStep _selectedProductionStep;
        private bool _isVisible;
        private DateTime _selectedDate;
        private TimeSpan _startTime;
        private TimeSpan _endTime;
        private string _workDescription;
        private int _manualMinutes;
        private Employee _selectedReporter;
        private string _selectedStatus;
        private bool _isFormVisible;
        private bool _isEditMode;
        private TimeLog _currentEditingTimeLog;

        public ProductionStep SelectedProductionStep
        {
            get => _selectedProductionStep;
            set
            {
                _selectedProductionStep = value;
                OnPropertyChanged(nameof(SelectedProductionStep));
                OnPropertyChanged(nameof(StepDisplayText));
                OnPropertyChanged(nameof(ProductDisplayText));
            }
        }

        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                _isVisible = value;
                OnPropertyChanged(nameof(IsVisible));
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                _selectedDate = value;
                OnPropertyChanged(nameof(SelectedDate));
            }
        }

        public TimeSpan StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                UpdateCalculatedMinutes();
            }
        }

        public TimeSpan EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                UpdateCalculatedMinutes();
            }
        }

        public string WorkDescription
        {
            get => _workDescription;
            set
            {
                _workDescription = value;
                OnPropertyChanged(nameof(WorkDescription));
            }
        }

        public int ManualMinutes
        {
            get => _manualMinutes;
            set
            {
                _manualMinutes = value;
                OnPropertyChanged(nameof(ManualMinutes));
            }
        }

        public Employee SelectedReporter
        {
            get => _selectedReporter;
            set
            {
                _selectedReporter = value;
                OnPropertyChanged(nameof(SelectedReporter));
            }
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                _selectedStatus = value;
                OnPropertyChanged(nameof(SelectedStatus));
            }
        }

        public bool IsFormVisible
        {
            get => _isFormVisible;
            set
            {
                _isFormVisible = value;
                OnPropertyChanged(nameof(IsFormVisible));
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged(nameof(IsEditMode));
                OnPropertyChanged(nameof(SaveButtonText));
            }
        }

        public TimeLog CurrentEditingTimeLog
        {
            get => _currentEditingTimeLog;
            set
            {
                _currentEditingTimeLog = value;
                OnPropertyChanged(nameof(CurrentEditingTimeLog));
            }
        }

        // Collections for ComboBoxes
        public ObservableCollection<Employee> AvailableReporters { get; private set; }
        public ObservableCollection<string> StatusOptions { get; private set; }

        // Collection for existing time logs
        public ObservableCollection<TimeLog> ExistingTimeLogs { get; private set; }

        // Display properties for UI
        public string StepDisplayText => SelectedProductionStep != null 
            ? $"Step {SelectedProductionStep.StepNumber}: {SelectedProductionStep.StepDescription}"
            : "No step selected";

        public string ProductDisplayText => SelectedProductionStep != null
            ? $"Job Number: {GetJobNumberForStep(SelectedProductionStep)}"
            : "";

        public string SaveButtonText => IsEditMode ? "Update Time Log" : "Save Time Log";

        // Commands
        public ICommand SaveTimeLogCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand CloseCommand { get; }
        public ICommand AddNewTimeLogCommand { get; }
        public ICommand EditTimeLogCommand { get; }
        public ICommand DeleteTimeLogCommand { get; }

        public TimeLogSideDrawerViewModel(DataAccess dataAccess = null, SharedDataService sharedDataService = null)
        {
            _dataAccess = dataAccess ?? new DataAccess();
            _sharedDataService = sharedDataService;

            // Initialize collections
            AvailableReporters = new ObservableCollection<Employee>();
            StatusOptions = new ObservableCollection<string> { "To do", "Doing", "Done" };
            ExistingTimeLogs = new ObservableCollection<TimeLog>();

            // Initialize with default values
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0); // 9:00 AM
            EndTime = new TimeSpan(17, 0, 0);  // 5:00 PM
            WorkDescription = string.Empty;
            SelectedStatus = "Doing"; // Default status for time logging context
            IsFormVisible = false; // Form is hidden by default
            IsEditMode = false; // Start in add mode
            CurrentEditingTimeLog = null;

            // Initialize commands
            SaveTimeLogCommand = new RelayCommand(async (p) => await ExecuteSaveTimeLogAsync(), CanExecuteSaveTimeLog);
            CancelCommand = new RelayCommand(ExecuteCancel);
            CloseCommand = new RelayCommand(ExecuteClose);
            AddNewTimeLogCommand = new RelayCommand(ExecuteAddNewTimeLog);
            EditTimeLogCommand = new RelayCommand(ExecuteEditTimeLog);
            DeleteTimeLogCommand = new RelayCommand(async (p) => await ExecuteDeleteTimeLogAsync(p));

            // Load available reporters
            LoadAvailableReporters();

            UpdateCalculatedMinutes();
        }

        private void UpdateCalculatedMinutes()
        {
            if (EndTime > StartTime)
            {
                _manualMinutes = (int)(EndTime - StartTime).TotalMinutes;
            }
            else
            {
                _manualMinutes = 0;
            }
            OnPropertyChanged(nameof(ManualMinutes));
        }

        private bool CanExecuteSaveTimeLog(object parameter)
        {
            return SelectedProductionStep != null &&
                   ManualMinutes > 0 &&
                   SelectedReporter != null &&
                   !string.IsNullOrWhiteSpace(WorkDescription) &&
                   !string.IsNullOrWhiteSpace(SelectedStatus);
        }

        private async System.Threading.Tasks.Task ExecuteSaveTimeLogAsync()
        {
            if (SelectedProductionStep == null || SelectedReporter == null) return;

            try
            {
                if (IsEditMode && CurrentEditingTimeLog != null)
                {
                    // Edit mode: Update existing time log
                    await UpdateExistingTimeLogAsync();
                }
                else
                {
                    // Add mode: Create new time log
                    await CreateNewTimeLogAsync();
                }

                // Reset form but keep drawer open
                ResetForm();
            }
            catch (Exception ex)
            {
                // TODO: Implement proper error handling/logging
                System.Windows.MessageBox.Show($"Error saving time log: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task CreateNewTimeLogAsync()
        {
            // Create new time log entry
            var timeLog = new TimeLog
            {
                ProductionStepId = SelectedProductionStep.StepId,
                EmployeeId = SelectedReporter.EmpId,
                ReporterName = SelectedReporter.EmpName,
                LoggedDate = SelectedDate,
                StartTime = StartTime,
                EndTime = EndTime,
                DurationMinutes = ManualMinutes,
                WorkDescription = WorkDescription ?? string.Empty,
                Status = SelectedStatus ?? "Doing",
                CreatedAt = DateTime.Now
            };

            // Save individual time log entry to database
            var newTimeLogId = await _dataAccess.AddTimeLogAsync(timeLog);
            timeLog.TimeLogId = newTimeLogId;

            // Update the TimeSpent property on ProductionStep
            SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + ManualMinutes;
            await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

            // Add to the existing time logs collection
            ExistingTimeLogs.Insert(0, timeLog); // Insert at beginning for newest first

            // Notify shared data service of the change
            _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
        }

        private async System.Threading.Tasks.Task UpdateExistingTimeLogAsync()
        {
            // Calculate the difference in minutes for ProductionStep TimeSpent adjustment
            var originalMinutes = CurrentEditingTimeLog.DurationMinutes;
            var newMinutes = ManualMinutes;
            var minutesDifference = newMinutes - originalMinutes;

            // Update the existing time log with new values
            CurrentEditingTimeLog.LoggedDate = SelectedDate;
            CurrentEditingTimeLog.StartTime = StartTime;
            CurrentEditingTimeLog.EndTime = EndTime;
            CurrentEditingTimeLog.DurationMinutes = ManualMinutes;
            CurrentEditingTimeLog.WorkDescription = WorkDescription ?? string.Empty;
            CurrentEditingTimeLog.Status = SelectedStatus ?? "Doing";
            CurrentEditingTimeLog.EmployeeId = SelectedReporter.EmpId;
            CurrentEditingTimeLog.ReporterName = SelectedReporter.EmpName;

            // Update in database
            await _dataAccess.UpdateTimeLogAsync(CurrentEditingTimeLog);

            // Update the TimeSpent property on ProductionStep
            SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + minutesDifference;
            await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

            // Update the item in the ObservableCollection in-place
            // The existing item in ExistingTimeLogs should automatically reflect changes
            // since it's the same object reference, but we'll trigger property change notifications
            var existingItem = ExistingTimeLogs.FirstOrDefault(tl => tl.TimeLogId == CurrentEditingTimeLog.TimeLogId);
            if (existingItem != null)
            {
                // Force UI refresh by removing and re-inserting at the same position
                var index = ExistingTimeLogs.IndexOf(existingItem);
                ExistingTimeLogs.RemoveAt(index);
                ExistingTimeLogs.Insert(index, CurrentEditingTimeLog);
            }

            // Notify shared data service of the change
            _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
        }

        private void ExecuteCancel(object parameter)
        {
            // Reset form but keep drawer open
            ResetForm();
        }

        private void ExecuteClose(object parameter)
        {
            // Close the drawer without saving (same as Cancel)
            IsVisible = false;

            // Reset form
            ResetForm();
        }

        private void ExecuteAddNewTimeLog(object parameter)
        {
            // Clear the form fields first, then show the form controls
            // Keep the same ProductionStep context
            ResetFormFields();
            IsEditMode = false;
            CurrentEditingTimeLog = null;
            IsFormVisible = true;
        }

        private void ResetFormFields()
        {
            SelectedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0);
            EndTime = new TimeSpan(17, 0, 0);
            WorkDescription = string.Empty;
            SelectedReporter = null;
            SelectedStatus = "Doing"; // Reset to default status
            UpdateCalculatedMinutes();
        }

        private void ResetForm()
        {
            ResetFormFields();
            IsFormVisible = false; // Hide form by default when resetting
            IsEditMode = false; // Reset to add mode
            CurrentEditingTimeLog = null; // Clear editing context
        }

        private void ExecuteEditTimeLog(object parameter)
        {
            if (parameter is TimeLog timeLog)
            {
                // Set edit mode and store the time log being edited
                IsEditMode = true;
                CurrentEditingTimeLog = timeLog;

                // Populate form with existing time log data
                SelectedDate = timeLog.LoggedDate;
                StartTime = timeLog.StartTime;
                EndTime = timeLog.EndTime;
                WorkDescription = timeLog.WorkDescription;
                SelectedStatus = timeLog.Status;

                // Find and select the reporter
                SelectedReporter = AvailableReporters.FirstOrDefault(r => r.EmpId == timeLog.EmployeeId);

                // Show the form
                IsFormVisible = true;
            }
        }

        private async System.Threading.Tasks.Task ExecuteDeleteTimeLogAsync(object parameter)
        {
            if (parameter is TimeLog timeLog)
            {
                try
                {
                    // Confirm deletion
                    var result = System.Windows.MessageBox.Show(
                        $"Are you sure you want to delete this time log entry?\n\nReporter: {timeLog.ReporterName}\nDate: {timeLog.LoggedDateFormatted}\nDuration: {timeLog.DurationFormatted}",
                        "Confirm Delete",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);

                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        // Delete from database
                        await _dataAccess.DeleteTimeLogAsync(timeLog.TimeLogId);

                        // Remove from collection
                        ExistingTimeLogs.Remove(timeLog);

                        // Update ProductionStep TimeSpent by recalculating from remaining entries
                        var totalMinutes = ExistingTimeLogs.Sum(tl => tl.DurationMinutes);
                        SelectedProductionStep.TimeSpent = totalMinutes;
                        await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);

                        // Notify shared data service of the change
                        _sharedDataService?.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Error deleting time log: {ex.Message}", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        public async void ShowForProductionStep(ProductionStep step)
        {
            SelectedProductionStep = step;
            IsVisible = true;
            ResetForm();

            // Load existing time logs for this production step
            await LoadExistingTimeLogsAsync();
        }

        private async System.Threading.Tasks.Task LoadExistingTimeLogsAsync()
        {
            if (SelectedProductionStep == null) return;

            try
            {
                var timeLogs = await _dataAccess.GetTimeLogsForProductionStepAsync(SelectedProductionStep.StepId);
                ExistingTimeLogs.Clear();
                foreach (var timeLog in timeLogs)
                {
                    ExistingTimeLogs.Add(timeLog);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading time logs: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private string GetJobNumberForStep(ProductionStep step)
        {
            if (step == null) return "Unknown";

            // Find the parent Product by ProductId using SharedDataService
            var parentProduct = _sharedDataService?.Products?.FirstOrDefault(p => p.ProductId == step.ProductId);
            return parentProduct?.JobNumber ?? "Unknown";
        }

        private void LoadAvailableReporters()
        {
            AvailableReporters.Clear();

            if (_sharedDataService?.Employees != null)
            {
                foreach (var employee in _sharedDataService.Employees.Where(e => e.IsActive))
                {
                    AvailableReporters.Add(employee);
                }
            }

            OnPropertyChanged(nameof(AvailableReporters));
        }
    }
}
