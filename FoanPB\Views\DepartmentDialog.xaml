﻿<Window x:Class="FoanPB.Views.DepartmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FoanPB.Views"
        mc:Ignorable="d"
        Title="Department" Height="450" Width="800">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Add Department Section -->
        <StackPanel Orientation="Horizontal" Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,10">
            <Label Content="New Department Name:" VerticalAlignment="Center"/>
            <TextBox Width="200" Text="{Binding NewDepartmentName, UpdateSourceTrigger=PropertyChanged}" Margin="5,0,5,0"/>
            <Button Content="Add" Command="{Binding AddCommand}" Width="75" Margin="5"/>
        </StackPanel>

        <!-- Departments DataGrid -->
        <DataGrid ItemsSource="{Binding Departments}" 
                  SelectedItem="{Binding SelectedDepartment}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  Grid.Row="1"
                  Margin="0,0,0,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Department ID" Binding="{Binding DepartmentID}" IsReadOnly="True"/>
                <DataGridTextColumn Header="Department Name" Binding="{Binding DepartmentName}" Width="*"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Update and Delete Buttons -->
        <StackPanel Orientation="Horizontal" Grid.Row="2" HorizontalAlignment="Center">
            <Button Content="Update" Command="{Binding UpdateCommand}" Width="75" Margin="5"/>
            <Button Content="Delete" Command="{Binding DeleteCommand}" Width="75" Margin="5"/>
        </StackPanel>
    </Grid>
</Window>
