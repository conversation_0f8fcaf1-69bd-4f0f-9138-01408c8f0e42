﻿// SharedDataService.cs
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace FoanPB.ViewModels
{
    /// <summary>
    /// A singleton-style data cache that other view-models bind to.
    /// All ObservableCollection editing happens on the dispatcher thread.
    /// </summary>
    public class SharedDataService : BaseViewModel
    {
        private readonly Dispatcher _uiDispatcher;

        public ObservableCollection<Product> Products { get; private set; }
        public ObservableCollection<Employee> Employees { get; private set; }
        public ObservableCollection<Equipment> Equipment { get; private set; }
        public ObservableCollection<StepType> StepTypes { get; private set; }

        private Product _selectedProduct;
        public Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged(nameof(SelectedProduct));
            }
        }

        public SharedDataService()
        {
            _uiDispatcher = Application.Current.Dispatcher;

            Products = new ObservableCollection<Product>();
            Employees = new ObservableCollection<Employee>();
            Equipment = new ObservableCollection<Equipment>();
            StepTypes = new ObservableCollection<StepType>();
        }

        #region Data loading ---------------------------------------------------------------------

        /// <summary>
        /// One-shot initial load – simply calls <see cref="RefreshDataAsync"/>.
        /// </summary>
        public Task InitializeAsync() => RefreshDataAsync();

        /// <summary>
        /// Re-fetches data without blocking the UI thread and updates the bound collections.
        /// </summary>
        public async Task RefreshDataAsync()
        {
            var dataAccess = new DataAccess();

            // 1) Fetch on background threads (OleDb is blocking, so wrap with Task.Run).
            var productsTask = Task.Run(() => dataAccess.GetAllProductsAsync());
            var employeesTask = Task.Run(() => dataAccess.GetAllEmployeesAsync());
            var equipmentTask = Task.Run(() => dataAccess.GetAllEquipmentAsync());
            var stepTypesTask = Task.Run(() => dataAccess.GetAllStepTypesAsync());

            await Task.WhenAll(productsTask, employeesTask, equipmentTask, stepTypesTask)
                      .ConfigureAwait(false);

            // 2) Build fresh collections off-thread.
            var newProducts = new ObservableCollection<Product>(productsTask.Result);
            var newEmployees = new ObservableCollection<Employee>(employeesTask.Result);
            var newEquipment = new ObservableCollection<Equipment>(equipmentTask.Result);
            var newStepTypes = new ObservableCollection<StepType>(stepTypesTask.Result);

            // 3) Marshal updates back to the dispatcher in a single hop.
            await _uiDispatcher.InvokeAsync(() =>
            {
                Products = newProducts;
                Employees = newEmployees;
                Equipment = newEquipment;
                StepTypes = newStepTypes;

                OnPropertyChanged(nameof(Products));
                OnPropertyChanged(nameof(Employees));
                OnPropertyChanged(nameof(Equipment));
                OnPropertyChanged(nameof(StepTypes));
            });
        }

        #endregion

        #region CRUD helpers ---------------------------------------------------------------------

        // Employees ---------------------------------------------------------------------------

        public void AddEmployee(Employee newEmployee)
        {
            DataAccess.AddEmployee(newEmployee);
            Employees.Add(newEmployee);
        }

        public void UpdateEmployee(Employee employee) =>
            DataAccess.UpdateEmployee(employee);

        public void DeleteEmployee(int employeeId)
        {
            DataAccess.DeleteEmployee(employeeId);
            var toRemove = Employees.FirstOrDefault(e => e.EmpId == employeeId);
            if (toRemove != null) Employees.Remove(toRemove);
        }

        // Equipment ---------------------------------------------------------------------------

        public void AddEquipment(Equipment newEquipment)
        {
            DataAccess.AddEquipment(newEquipment);
            Equipment.Add(newEquipment);
        }

        public void UpdateEquipment(Equipment equipment) =>
            DataAccess.UpdateEquipment(equipment);

        public void DeleteEquipment(int equipmentId)
        {
            DataAccess.DeleteEquipment(equipmentId);
            var toRemove = Equipment.FirstOrDefault(eq => eq.EquipmentId == equipmentId);
            if (toRemove != null) Equipment.Remove(toRemove);
        }

        // Step types --------------------------------------------------------------------------

        public void AddStepType(StepType newStepType)
        {
            DataAccess.AddStepType(newStepType);
            StepTypes.Add(newStepType);
        }

        public void UpdateStepType(StepType stepType) =>
            DataAccess.UpdateStepType(stepType);

        public void DeleteStepType(int typeId)
        {
            DataAccess.DeleteStepType(typeId);
            var toRemove = StepTypes.FirstOrDefault(st => st.TypeId == typeId);
            if (toRemove != null) StepTypes.Remove(toRemove);
        }

        #endregion

        #region Product completion tracking --------------------------------------------------

        /// <summary>
        /// Notifies that a ProductionStep's TimeSpent has been updated, which may affect the parent Product's IsCompleted status.
        /// This method finds the parent Product and triggers property change notifications for computed properties.
        /// </summary>
        /// <param name="productionStep">The ProductionStep that was updated</param>
        public void NotifyProductionStepTimeSpentChanged(ProductionStep productionStep)
        {
            if (productionStep == null) return;

            // Find the parent Product by ProductId
            var parentProduct = Products?.FirstOrDefault(p => p.ProductId == productionStep.ProductId);
            if (parentProduct != null)
            {
                // Notify that computed properties dependent on ProductionSteps have changed
                parentProduct.NotifyProductionStepsChanged();
            }
        }

        /// <summary>
        /// Notifies that multiple ProductionSteps have been updated for a specific Product.
        /// This is more efficient than calling NotifyProductionStepTimeSpentChanged multiple times for the same Product.
        /// </summary>
        /// <param name="productId">The ID of the Product whose steps were updated</param>
        public void NotifyProductTimeSpentChanged(int productId)
        {
            var product = Products?.FirstOrDefault(p => p.ProductId == productId);
            if (product != null)
            {
                product.NotifyProductionStepsChanged();
            }
        }

        #endregion
    }
}
