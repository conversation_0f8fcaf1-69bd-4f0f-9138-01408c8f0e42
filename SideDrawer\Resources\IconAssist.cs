﻿using System.Windows;
using System.Windows.Media;

namespace SideDrawer // Change to your project's namespace
{
    public static class IconAssist
    {
        public static readonly DependencyProperty IconProperty =
            DependencyProperty.RegisterAttached(
                "Icon",
                typeof(Geometry),
                typeof(IconAssist),
                new PropertyMetadata(default(Geometry))
            );

        public static void SetIcon(UIElement element, Geometry value)
        {
            element.SetValue(IconProperty, value);
        }

        public static Geometry GetIcon(UIElement element)
        {
            return (Geometry)element.GetValue(IconProperty);
        }
    }
}