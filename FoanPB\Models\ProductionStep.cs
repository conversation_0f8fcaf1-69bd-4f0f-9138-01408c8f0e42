﻿using FoanPB.ViewModels;

namespace FoanPB.Models
{
    public enum ProcessStatus
    {
        NotStarted,
        Running,
        Paused,
        Completed
    }
    public class ProductionStep : BaseViewModel
    {
        public int StepId { get; set; } // Unique identifier for the production step
        public int ProductId { get; set; } // ID of the product this step is part of

        private int _stepNumber;
        public int StepNumber
        {
            get => _stepNumber;
            set
            {
                if (_stepNumber != value)
                {
                    _stepNumber = value;
                    OnPropertyChanged(nameof(StepNumber));
                }
            }
        }

        private string? _stepDescription; //Artwork, Cutting...
        public string? StepDescription
        {
            get => _stepDescription;
            set
            {
                if (_stepDescription != value)
                {
                    _stepDescription = value;
                    OnPropertyChanged(nameof(StepDescription));
                }
            }
        }

        private DateTime? _stepTargetDate;
        public DateTime? StepTargetDate
        {
            get => _stepTargetDate;
            set
            {
                if (_stepTargetDate != value)
                {
                    _stepTargetDate = value;
                    OnPropertyChanged(nameof(StepTargetDate));
                }
            }
        }
        public int? Estmin { get; set; } // Duration required to complete this step in minutes

        private int? _timeSpent;
        public int? TimeSpent
        {
            get { return _timeSpent; }
            set
            {
                _timeSpent = value;
                OnPropertyChanged(nameof(TimeSpent));
            }
        }

        public string? EquipmentName { get; set; }

        public string? OperatorName { get; set; }

        private string _stepStatus;
        public string StepStatus
        {
            get => _stepStatus;
            set
            {
                if (_stepStatus != value)
                {
                    _stepStatus = value;
                    OnPropertyChanged(nameof(StepStatus));
                }
            }
        }

        // Parameterless constructor
        public ProductionStep()
        {
            // Initialize with default values if needed
            StepId = 0;  // Example default value, adjust as needed
            ProductId = 1; // Assuming a default ProductId for demonstration
            _stepStatus = "To do"; // Default status for new production steps
        }
        public ProductionStep(int stepId, int productId, int stepNumber, string stepDescription, int estmin)
        {
            StepId = stepId;
            ProductId = productId;
            StepNumber = stepNumber;
            StepDescription = stepDescription;
            Estmin = estmin;
        }
        private ProcessStatus _status;
        public ProcessStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        private DateTime? _startTime;
        public DateTime? StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
            }
        }

        private TimeSpan _totalElapsedTime;
        public TimeSpan TotalElapsedTime
        {
            get => _totalElapsedTime;
            set
            {
                _totalElapsedTime = value;
                OnPropertyChanged(nameof(TotalElapsedTime));
                OnPropertyChanged(nameof(TimeSpent));
            }
        }

        //private int? _timeSpent;
        //public int? TimeSpent
        //{
        //    get => (int?)_totalElapsedTime.TotalMinutes;
        //    set
        //    {
        //        if (value.HasValue)
        //        {
        //            TotalElapsedTime = TimeSpan.FromMinutes(value.Value);
        //        }
        //        else
        //        {
        //            TotalElapsedTime = TimeSpan.Zero;
        //        }
        //        OnPropertyChanged(nameof(TimeSpent));
        //    }
    }
}

