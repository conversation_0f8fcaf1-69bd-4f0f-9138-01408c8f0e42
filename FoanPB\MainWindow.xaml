﻿<Window x:Class="FoanPB.MainWindow" 
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:local="clr-namespace:FoanPB" 
        mc:Ignorable="d" 
        Title="Production Book - Management System" 
        Height="600" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Window.Resources>
        <!-- **Icon Styles using Paths (Vector Graphics)** -->

        <!-- Job Input Icon (Document with Plus) -->
        <Style x:Key="JobInputIcon" TargetType="Path">
            <Setter Property="Fill" Value="#2E86AB"/>
            <Setter Property="Data" Value="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zM6 20V4h7v5h5v11H6z M10 12h4 M12 10v4"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Cutting Icon (Scissors) -->
        <!--<Style x:Key="CuttingIcon" TargetType="Path">
            <Setter Property="Fill" Value="#F18F01"/>
            <Setter Property="Data" Value="M6 6l6 6 6-6M9 3a3 3 0 1 0 0 6 3 3 0 0 0 0-6zM21 15a3 3 0 1 0 0 6 3 3 0 0 0 0-6zM12 12l6 6M3 15a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>-->
        <Style x:Key="CuttingIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#F18F01"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <!--<Setter Property="Data" Value="M4 4a2 2 0 1 0 0 4a2 2 0 0 0 0-4z
                                    M4 16a2 2 0 1 0 0 4a2 2 0 0 0 0-4z
                                    M6 6l12 12
                                    M6 18l12-12
                                    M10 10l2 2"/>-->
            <!--<Setter Property="Data" Value="M7 4a3 3 0 1 1 0 6a3 3 0 0 1 0-6z M7 14a3 3 0 1 1 0 6a3 3 0 0 1 0-6z M8.5 10l9.5-6.5 M8.5 10l9.5 6.5"/>-->
            <Setter Property="Data" Value="M7 4a3 3 0 1 1 0 6a3 3 0 0 1 0-6zM7 14a3 3 0 1 1 0 6a3 3 0 0 1 0-6zM8.5 10l12-8M8.5 10l12 8"/>
            
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Straight Sew Icon (Thread/Needle) -->
        <!--<Style x:Key="StraightSewIcon" TargetType="Path">
            <Setter Property="Fill" Value="#C73E1D"/>
            <Setter Property="Data" Value="M12 2l8 8-8 8-8-8 8-8z M8 12h8 M12 8v8"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>-->

        <Style x:Key="StraightSewIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#F18F01"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <Setter Property="Data" Value="M2 10h2M6 10h2M10 10h2M14 10h2M18 10h2"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Artwork Icon (Palette) -->
        <!--<Style x:Key="ArtworkIcon" TargetType="Path">
            <Setter Property="Fill" Value="#9B59B6"/>
            <Setter Property="Data" Value="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.54 0 3-0.37 4.24-1.03 0.65-0.34 0.94-1.12 0.6-1.77-0.34-0.65-1.12-0.94-1.77-0.6C14.23 18.89 13.15 19 12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7c0 1.15-0.11 2.23-0.4 3.07 0.34 0.65 1.12 0.94 1.77 0.6C21.63 15 22 13.54 22 12c0-5.52-4.48-10-10-10z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>-->

        <Style x:Key="ArtworkIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#9B59B6"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <Setter Property="Data" Value="M12 2a10 10 0 0 0 0 20c1.7 0 3-1.3 3-3 0-1.1-.9-2-2-2h-1c-1.1 0-2-.9-2-2 0-.6.2-1.2.7-1.6.5-.4 1.1-.6 1.8-.4 2.3.6 4.5-1.2 4.5-3.6C17 5.5 14.1 2 12 2z
                                    M8 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0z
                                    M10 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0z
                                    M14 6a1 1 0 1 1-2 0 1 1 0 0 1 2 0z
                                    M16 10a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Applique Icon (Layers) -->
        <!--<Style x:Key="AppliqueIcon" TargetType="Path">
            <Setter Property="Fill" Value="#E67E22"/>
            <Setter Property="Data" Value="M12 2l3 3-3 3-3-3 3-3z M12 8l3 3-3 3-3-3 3-3z M12 14l3 3-3 3-3-3 3-3z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>-->

        <Style x:Key="AppliqueIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#C73E1D"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <Setter Property="Data" Value="
        M4 4h10v10h-10z
        M6 4v2 M8 4v2 M10 4v2
        M12 4v2 M14 4v2
        M4 6h2 M4 8h2 M4 10h2
        M4 12h2 M4 14h2
        M6 14v2 M8 14v2 M10 14v2
        M12 14v2 M14 14v2
        M14 6h2 M14 8h2 M14 10h2
        M14 12h2"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Headings Icon (Text/Typography) -->
        <Style x:Key="HeadingsIcon" TargetType="Path">
            <Setter Property="Fill" Value="#27AE60"/>
            <Setter Property="Data" Value="M4 6h16v2H4V6z M4 10h16v2H4v-2z M4 14h10v2H4v-2z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Despatch Icon (Truck) -->
        <!--<Style x:Key="DespatchIcon" TargetType="Path">
            <Setter Property="Fill" Value="#3498DB"/>
            <Setter Property="Data" Value="M1 3v12h4a3 3 0 1 0 6 0h4a3 3 0 1 0 6 0h1V9l-4-4h-4V3H1z M8 18a1 1 0 1 1 0-2 1 1 0 0 1 0 2z M18 18a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>-->

        <Style x:Key="DespatchIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#3498DB"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <Setter Property="Data" Value="
        M3 13V6a1 1 0 0 1 1-1h9v8H3z
        M13 6h4l3 4v3h-7V6z
        M5 18a2 2 0 1 1 0-4a2 2 0 0 1 0 4z
        M17 18a2 2 0 1 1 0-4a2 2 0 0 1 0 4z
    "/>
            <Setter Property="Width" Value="24"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <Style x:Key="StatisticsIcon" TargetType="Path">
            <Setter Property="Stroke" Value="#8E44AD"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="Fill" Value="Transparent"/>
            <Setter Property="Data" Value="M4 16V10H6V16H4ZM8 16V6H10V16H8ZM12 16V12H14V16H12ZM16 16V4H18V16H16Z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- Inventory Icon (Boxes) -->
        <Style x:Key="InventoryIcon" TargetType="Path">
            <Setter Property="Fill" Value="#16A085"/>
            <Setter Property="Data" Value="M4 3h16v5H4V3z M4 10h16v5H4v-5z M4 17h16v4H4v-4z"/>
            <Setter Property="Width" Value="18"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Stretch" Value="Uniform"/>
        </Style>

        <!-- **Button Styles with Increased Height** -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#34495E"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="10 10"/>
            <Setter Property="MinHeight" Value="30"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2C3E50"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1A252F"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#ECF0F1">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- **Navigation Panel** -->
        <Border Grid.Column="0" 
                Background="#2C3E50" 
                BorderThickness="0,0,1,0" 
                BorderBrush="#BDC3C7">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="15">

                    <!-- **Application Title** -->
                    <TextBlock Text="Production Book" 
                               Foreground="White" 
                               FontSize="18" 
                               FontWeight="Bold" 
                               TextAlignment="Center" 
                               Margin="0,10,0,25"/>

                    <!-- **Job Input Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="NewOrder">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource JobInputIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Job Input" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Cutting Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Cutting">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource CuttingIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Cutting" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Straight Sew Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="StraightSew">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource StraightSewIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Straight Sew" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Artwork Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Artwork">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource ArtworkIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Artwork" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Applique Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Applique">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource AppliqueIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Applique" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Headings Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Headings">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource HeadingsIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Headings" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Despatch Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Despatch">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource DespatchIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Despatch" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Statistics Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Statistics">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource StatisticsIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Statistics" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- **Inventory Button** -->
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding UpdateViewCommand}" 
                            CommandParameter="Inventory">
                        <StackPanel Orientation="Horizontal">
                            <Path Style="{StaticResource InventoryIcon}" Margin="0,0,12,0"/>
                            <TextBlock Text="Inventory" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- **Main Content Area** -->
        <ContentControl Grid.Column="1" 
                        Content="{Binding SelectedViewModel}" 
                        Margin="20">
            <ContentControl.Style>
                <Style TargetType="ContentControl">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding SelectedViewModel}" Value="{x:Null}">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <Border Background="White" 
                                                CornerRadius="8" 
                                                BorderThickness="1" 
                                                BorderBrush="#BDC3C7">
                                            <StackPanel HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center">
                                                <TextBlock Text="📊" 
                                                         FontSize="48" 
                                                         HorizontalAlignment="Center" 
                                                         Margin="0,0,0,20"/>
                                                <TextBlock Text="Production Book" 
                                                         FontSize="32" 
                                                         FontWeight="Bold" 
                                                         HorizontalAlignment="Center" 
                                                         Foreground="#2C3E50"
                                                         Margin="0,0,0,10"/>
                                                <TextBlock Text="Manufacturing Management System" 
                                                         FontSize="16" 
                                                         HorizontalAlignment="Center" 
                                                         Foreground="#7F8C8D"/>
                                                <TextBlock Text="Select a module from the navigation panel to begin" 
                                                         FontSize="14" 
                                                         HorizontalAlignment="Center" 
                                                         Foreground="#95A5A6"
                                                         Margin="0,20,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ContentControl.Style>
        </ContentControl>
    </Grid>
</Window>
