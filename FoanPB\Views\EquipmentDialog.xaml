﻿<Window x:Class="FoanPB.Views.EquipmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FoanPB.Views"
        mc:Ignorable="d"
        Title="EquipmentDialog" Height="450" Width="800">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Add Equipment Section -->
        <StackPanel Orientation="Horizontal" Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,10">
            <Label Content="New Equipment Name:" VerticalAlignment="Center"/>
            <TextBox Width="200" Text="{Binding NewEquipmentName, UpdateSourceTrigger=PropertyChanged}" Margin="5,0,5,0"/>
            <Button Content="Add" Command="{Binding AddCommand}" Width="75" Margin="5"/>
        </StackPanel>

        <!-- Equipments DataGrid -->
        <DataGrid ItemsSource="{Binding Equipment}" 
                  SelectedItem="{Binding SelectedEquipment}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  Grid.Row="1"
                  Margin="0,0,0,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Equipment ID" Binding="{Binding EquipmentId}" IsReadOnly="True"/>
                <DataGridTextColumn Header="Equipment Name" Binding="{Binding EquipmentName}" Width="*"/>
                <!-- Add more columns here if your Equipment model has additional properties -->
            </DataGrid.Columns>
        </DataGrid>

        <!-- Update and Delete Buttons -->
        <StackPanel Orientation="Horizontal" Grid.Row="2" HorizontalAlignment="Center">
            <Button Content="Update" Command="{Binding UpdateCommand}" Width="75" Margin="5"/>
            <Button Content="Delete" Command="{Binding DeleteCommand}" Width="75" Margin="5"/>
        </StackPanel>
    </Grid>
</Window>
