﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using FoanPB.DataService;
using FoanPB.Models;

namespace FoanPB.Views
{
    public partial class DespatchView : UserControl
    {
        public DespatchView()
        {
            InitializeComponent();
        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                // Update time spent for dispatch steps on cell edit
                Dispatcher.BeginInvoke(async () =>
                {
                    if (e.Row.Item is ProductionStep edited)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);

                            // Notify that the ProductionStep TimeSpent has changed, which may affect Product.IsCompleted
                            if (this.DataContext is ViewModels.DespatchViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (System.Exception ex)
                        {
                            MessageBox.Show($"Error updating dispatch step: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}
