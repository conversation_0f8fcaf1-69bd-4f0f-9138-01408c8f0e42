﻿// Start of file: App.xaml.cs
using FoanPB.DataService;
using FoanPB.Service;
using FoanPB.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System.Configuration;
using System.Windows;
using FoanPB.Views;
using SplashScreen = FoanPB.Views.SplashScreen;
using System.Diagnostics;
using System.Windows.Controls;
namespace FoanPB
{
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;
        private SplashScreen _splashScreen;
        public App()
        {
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;
        }
        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            Debug.WriteLine("App.OnStartup: Entered.");
            _splashScreen = new SplashScreen();
            _splashScreen.Show();
            Debug.WriteLine("App.OnStartup: Splash screen shown.");
            var serviceCollection = new ServiceCollection();
            ConfigureServices(serviceCollection);
            _serviceProvider = serviceCollection.BuildServiceProvider();
            Debug.WriteLine("App.OnStartup: Services configured and provider built.");
            var sharedDataService = _serviceProvider.GetRequiredService<SharedDataService>();
            bool dataLoadedSuccessfully = false;
            try
            {
                Debug.WriteLine("App.OnStartup: Starting data initialization on a background thread...");
                await Task.Run(async () =>
                {
                    await sharedDataService.InitializeAsync();
                });
                dataLoadedSuccessfully = true;
                Debug.WriteLine("App.OnStartup: Data initialization complete.");
            }
            catch (System.Exception ex)
            {
                Debug.WriteLine($"App.OnStartup: Error during data initialization: {ex.ToString()}");
                _splashScreen?.Close();
                MessageBox.Show($"Failed to preload data: {ex.Message}", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Current.Shutdown(-1);
                return;
            }
            if (dataLoadedSuccessfully)
            {
                try
                {
                    Debug.WriteLine("App.OnStartup: Getting MainWindow service...");
                    var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                    Debug.WriteLine("App.OnStartup: MainWindow service retrieved. Setting as Application.MainWindow.");
                    this.MainWindow = mainWindow;
                    this.ShutdownMode = ShutdownMode.OnMainWindowClose;
                    Debug.WriteLine("App.OnStartup: Showing MainWindow...");
                    mainWindow.Show();
                    Debug.WriteLine("App.OnStartup: MainWindow.Show() called.");
                    _splashScreen?.Close();
                    Debug.WriteLine("App.OnStartup: Splash screen closed.");
                }
                catch (System.Exception ex)
                {
                    Debug.WriteLine($"App.OnStartup: Error creating or showing MainWindow: {ex.ToString()}");
                    _splashScreen?.Close();
                    MessageBox.Show($"Failed to load the main application window: {ex.Message}", "Application Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Current.Shutdown(-1);
                }
            }
            Debug.WriteLine("App.OnStartup: Exiting.");
        }
        private void ConfigureServices(IServiceCollection services)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["AccessDbConnection"].ConnectionString;
            services.AddSingleton(new OperatorService(connectionString));
            services.AddSingleton(new DepartmentRepository(connectionString));
            services.AddSingleton<DataAccess>();
            services.AddSingleton<SharedDataService>();
            services.AddSingleton<TemplateService>(_ => new TemplateService(connectionString));
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<MainViewModel>();
            services.AddSingleton<OrderEntryViewModel>();
            services.AddTransient<NewOrderViewModel>(sp =>
                new NewOrderViewModel(
                    sp.GetRequiredService<SharedDataService>(),
                    sp.GetRequiredService<MainViewModel>(),
                    sp.GetRequiredService<OrderEntryViewModel>()));
            services.AddTransient<CuttingViewModel>();
            services.AddTransient<OperatorViewModel>();
            services.AddTransient<DepartmentViewModel>();
            services.AddTransient<StraightSewViewModel>();
            services.AddTransient<ArtworkViewModel>();
            services.AddTransient<AppliqueViewModel>();
            services.AddTransient<HeadingsViewModel>();
            services.AddTransient<DespatchViewModel>();
            services.AddTransient<StatisticsViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<MainWindow>();
        }
        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Debug.WriteLine($"App_DispatcherUnhandledException: {e.Exception.ToString()}");
            MessageBox.Show($"An unhandled UI error occurred: {e.Exception.Message}\n\nDetails: {e.Exception.ToString()}", "Unhandled UI Error", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }
        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            Debug.WriteLine($"TaskScheduler_UnobservedTaskException: {e.Exception.ToString()}");
            MessageBox.Show($"An unobserved task error occurred: {e.Exception.InnerExceptions.FirstOrDefault()?.Message ?? e.Exception.Message}\n\nDetails: {e.Exception.ToString()}", "Unobserved Task Error", MessageBoxButton.OK, MessageBoxImage.Error);
            e.SetObserved();
        }
    }
}
// End of file: App.xaml.cs