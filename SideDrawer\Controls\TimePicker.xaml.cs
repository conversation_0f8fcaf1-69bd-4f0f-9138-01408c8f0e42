﻿using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SideDrawer.Controls
{
    public partial class TimePicker : UserControl
    {
        // DependencyProperty to allow binding to the selected time from a ViewModel
        public static readonly DependencyProperty SelectedTimeProperty =
            DependencyProperty.Register("SelectedTime", typeof(TimeSpan?), typeof(TimePicker),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedTimeChanged));

        public TimeSpan? SelectedTime
        {
            get { return (TimeSpan?)GetValue(SelectedTimeProperty); }
            set { SetValue(SelectedTimeProperty, value); }
        }

        private bool _isInternalUpdate = false;

        public TimePicker()
        {
            InitializeComponent();
            PopulateTimeLists();
        }

        private void PopulateTimeLists()
        {
            // Populate Hours (1-12)
            HourList.ItemsSource = Enumerable.Range(1, 12).Select(h => h.ToString("D2"));
            // Populate Minutes (00-59 in 5-minute increments for usability)
            MinuteList.ItemsSource = Enumerable.Range(0, 12).Select(m => (m * 5).ToString("D2"));
            // Populate AM/PM
            AmPmList.ItemsSource = new[] { "AM", "PM" };
        }

        private static void OnSelectedTimeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (TimePicker)d;
            if (!control._isInternalUpdate)
            {
                control.UpdateTimeDisplay();
            }
        }

        private void TimeSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                // Scroll the newly selected item into view
                (sender as ListBox)?.ScrollIntoView(e.AddedItems[0]);

                UpdateSelectedTimeFromLists();
            }
        }

        private void UpdateSelectedTimeFromLists()
        {
            if (HourList.SelectedItem == null || MinuteList.SelectedItem == null || AmPmList.SelectedItem == null)
                return;

            int hour = int.Parse((string)HourList.SelectedItem);
            int minute = int.Parse((string)MinuteList.SelectedItem);
            string ampm = (string)AmPmList.SelectedItem;

            if (ampm == "PM" && hour != 12)
            {
                hour += 12;
            }
            if (ampm == "AM" && hour == 12) // Midnight case
            {
                hour = 0;
            }

            _isInternalUpdate = true;
            SelectedTime = new TimeSpan(hour, minute, 0);
            _isInternalUpdate = false;

            // Update the display text and close the popup after selection
            DisplayButton.Content = SelectedTime.Value.ToString(@"hh\:mm tt");
            TimePopup.IsOpen = false;
        }

        private void UpdateTimeDisplay()
        {
            if (SelectedTime.HasValue)
            {
                var time = SelectedTime.Value;
                DisplayButton.Content = time.ToString(@"hh\:mm tt");

                // Update the list boxes to match the current time
                int hour12 = time.Hours % 12;
                if (hour12 == 0) hour12 = 12; // 0 o'clock is 12 AM

                _isInternalUpdate = true;
                HourList.SelectedItem = hour12.ToString("D2");
                MinuteList.SelectedItem = (time.Minutes / 5 * 5).ToString("D2"); // Snap to nearest 5
                AmPmList.SelectedItem = time.Hours < 12 ? "AM" : "PM";
                _isInternalUpdate = false;
            }
            else
            {
                DisplayButton.Content = "Select a time...";
            }
        }
    }
}