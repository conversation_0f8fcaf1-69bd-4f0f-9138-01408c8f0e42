using System;
using FoanPB.ViewModels;

namespace FoanPB.Models
{
    public class TimeLog : BaseViewModel
    {
        private int _timeLogId;
        public int TimeLogId
        {
            get => _timeLogId;
            set
            {
                _timeLogId = value;
                OnPropertyChanged(nameof(TimeLogId));
            }
        }

        private int _productionStepId;
        public int ProductionStepId
        {
            get => _productionStepId;
            set
            {
                _productionStepId = value;
                OnPropertyChanged(nameof(ProductionStepId));
            }
        }

        private int _employeeId;
        public int EmployeeId
        {
            get => _employeeId;
            set
            {
                _employeeId = value;
                OnPropertyChanged(nameof(EmployeeId));
            }
        }

        private string _reporterName;
        public string ReporterName
        {
            get => _reporterName;
            set
            {
                _reporterName = value;
                OnPropertyChanged(nameof(ReporterName));
            }
        }

        private DateTime _loggedDate;
        public DateTime LoggedDate
        {
            get => _loggedDate;
            set
            {
                _loggedDate = value;
                OnPropertyChanged(nameof(LoggedDate));
            }
        }

        private TimeSpan _startTime;
        public TimeSpan StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                OnPropertyChanged(nameof(Duration));
                OnPropertyChanged(nameof(DurationFormatted));
            }
        }

        private TimeSpan _endTime;
        public TimeSpan EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                OnPropertyChanged(nameof(Duration));
                OnPropertyChanged(nameof(DurationFormatted));
            }
        }

        private int _durationMinutes;
        public int DurationMinutes
        {
            get => _durationMinutes;
            set
            {
                _durationMinutes = value;
                OnPropertyChanged(nameof(DurationMinutes));
                OnPropertyChanged(nameof(DurationFormatted));
            }
        }

        private string _workDescription;
        public string WorkDescription
        {
            get => _workDescription;
            set
            {
                _workDescription = value;
                OnPropertyChanged(nameof(WorkDescription));
            }
        }

        private string _status;
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                _createdAt = value;
                OnPropertyChanged(nameof(CreatedAt));
            }
        }

        // Calculated properties for UI display
        public TimeSpan Duration => EndTime > StartTime ? EndTime - StartTime : TimeSpan.Zero;

        public string DurationFormatted
        {
            get
            {
                var totalMinutes = DurationMinutes > 0 ? DurationMinutes : (int)Duration.TotalMinutes;
                var hours = totalMinutes / 60;
                var minutes = totalMinutes % 60;
                
                if (hours > 0)
                    return $"{hours}h {minutes}m";
                else
                    return $"{minutes}m";
            }
        }

        public string LoggedDateFormatted => LoggedDate.ToString("MMM dd, yyyy");

        // Constructor
        public TimeLog()
        {
            _reporterName = string.Empty;
            _workDescription = string.Empty;
            _status = "Doing";
            LoggedDate = DateTime.Today;
            StartTime = new TimeSpan(9, 0, 0);
            EndTime = new TimeSpan(17, 0, 0);
            CreatedAt = DateTime.Now;
        }
    }
}
