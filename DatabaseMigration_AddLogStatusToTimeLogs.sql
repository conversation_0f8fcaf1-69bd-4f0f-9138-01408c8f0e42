-- Database Migration Script: Add LogStatus field to tblTimeLogs for Split-Session Time Logging
-- This script adds support for InProgress vs Completed time log entries
-- Run this script in your Microsoft Access database

-- IMPORTANT: BACKUP YOUR DATABASE BEFORE RUNNING THIS MIGRATION!

-- =====================================================
-- STEP 1: Add LogStatus column to tblTimeLogs
-- =====================================================

-- Add the new LogStatus column to tblTimeLogs table
ALTER TABLE tblTimeLogs ADD COLUMN LogStatus TEXT(50) DEFAULT 'Completed';

-- =====================================================
-- STEP 2: Update existing records to Completed status
-- =====================================================

-- Set all existing time log entries to 'Completed' status
-- This ensures backward compatibility with existing data
UPDATE tblTimeLogs SET LogStatus = 'Completed' WHERE LogStatus IS NULL;

-- =====================================================
-- STEP 3: Modify EndTime column to allow NULL values
-- =====================================================

-- Note: In Microsoft Access, you cannot directly modify a column to allow NULL
-- if it was previously NOT NULL. You need to create a new table structure.
-- This is handled in the application code by checking for NULL values.

-- For new InProgress entries, EndTime will be NULL until the timer is completed.
-- The application will handle this by:
-- 1. Allowing NULL EndTime when LogStatus = 'InProgress'
-- 2. Requiring EndTime when LogStatus = 'Completed'

-- =====================================================
-- STEP 4: Create indexes for better performance
-- =====================================================

-- Create index on LogStatus for efficient filtering
CREATE INDEX IX_TimeLogs_LogStatus ON tblTimeLogs (LogStatus);

-- =====================================================
-- STEP 5: Verification Queries
-- =====================================================

-- Verify the migration was successful
SELECT 'TimeLogs with LogStatus' as TableInfo, COUNT(*) as RecordCount 
FROM tblTimeLogs 
WHERE LogStatus IS NOT NULL;

SELECT 'Unique LogStatus values' as Info, LogStatus, COUNT(*) as Count
FROM tblTimeLogs 
GROUP BY LogStatus;

-- Show sample of updated records
SELECT TOP 5 TimeLogId, ProductionStepId, LoggedDate, StartTime, EndTime, LogStatus, CreatedAt
FROM tblTimeLogs
ORDER BY CreatedAt DESC;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- After running this migration:
-- 1. All existing time logs will have LogStatus = 'Completed'
-- 2. New InProgress entries can be created with NULL EndTime
-- 3. The application will handle split-session time logging workflow
-- 4. Test all functionality thoroughly
-- 5. Consider keeping a backup of the original database until you're confident the migration is successful

-- Expected LogStatus values:
-- 'InProgress' - Timer started but not yet completed (EndTime is NULL)
-- 'Completed' - Timer completed with both StartTime and EndTime recorded
