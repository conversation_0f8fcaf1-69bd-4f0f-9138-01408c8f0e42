﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using FoanPB.ViewModels;

namespace FoanPB.Models
{
    public class Product : BaseViewModel
    {
        public int ProductId { get; set; } // Unique identifier for the product
        public string JobNumber { get; set; }
        public string ProductName { get; set; } // Name of the product
        public string Description { get; set; } // Description of the product
        public int Length { get; set; } // Length of the product
        public int Width { get; set; } // Width of the product
        public string JobType { get; set; } // Type of the product
        public int Qty { get; set; } // Quantity of the product
        public string Rep { get; set; } // Start date for the product
        public string SKU { get; set; } // Start date for the product
        public bool IsUrgent { get; set; } // Start date for the product
        public DateTime? TargetDate { get; set; } // Target date for the product
        //public string Equipment { get; set; } // Equipment used for the product
        public List<ProductionStep> ProductionSteps { get; set; } // Collection of production steps
        // Read-only property to calculate total TimeSpent
        public int SubTimeSpent
        {
            get
            {
                // Sum the TimeSpent for all ProductionSteps, treating nulls as 0
                return ProductionSteps?.Sum(ps => ps.TimeSpent ?? 0) ?? 0;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this job is completed.
        /// A job is considered complete if and only if:
        /// 1. The Product has a ProductionSteps collection that is not null
        /// 2. The ProductionSteps collection contains at least one step
        /// 3. ALL steps in the ProductionSteps collection have a TimeSpent value that is not null and greater than 0
        /// </summary>
        public bool IsCompleted
        {
            get
            {
                // Check if ProductionSteps collection exists and has at least one step
                if (ProductionSteps == null || ProductionSteps.Count == 0)
                {
                    return false;
                }

                // Check if ALL steps have TimeSpent values that are not null and greater than 0
                return ProductionSteps.All(step => step.TimeSpent.HasValue && step.TimeSpent.Value > 0);
            }
        }
        public Product()
        {
            ProductionSteps = new List<ProductionStep>();
        }
        public Product(int productId, string jobNumber, string productName, string description, string jobType)
        {
            ProductId = productId;
            JobNumber = jobNumber;
            ProductName = productName;
            Description = description;
            ProductionSteps = new List<ProductionStep>();
            JobType = jobType;
        }

        /// <summary>
        /// Notifies that properties dependent on ProductionSteps have changed.
        /// This should be called whenever ProductionSteps are added, removed, or their TimeSpent values are modified.
        /// </summary>
        public void NotifyProductionStepsChanged()
        {
            OnPropertyChanged(nameof(SubTimeSpent));
            OnPropertyChanged(nameof(IsCompleted));
        }
    }
}
