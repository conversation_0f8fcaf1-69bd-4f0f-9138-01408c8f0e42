﻿// ArtworkViewModel.cs
using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class ArtworkViewModel : StepTypeViewModelBase
    {
        // Only show/process steps whose description is "Artwork"
        protected override string TargetStepDescription => "Artwork";

        // ctor wires into the shared data + data access logic in the base class
        public ArtworkViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
        }
    }
}
