﻿ <!--// Start of file: NewOrderView.xaml-->
    <UserControl x:Class="FoanPB.Views.NewOrderView" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:local="clr-namespace:FoanPB.Views" xmlns:sys="clr-namespace:System;assembly=mscorlib" mc:Ignorable="d" d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" Text="Job Input" FontSize="24" FontWeight="Bold" Margin="10" />
        <!-- Replaced StackPanel with a Grid for proper layouting -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                <Button Content="New Job" Height="25" Width="100" Margin="5" Command="{Binding DataContext.UpdateViewCommand, RelativeSource={RelativeSource AncestorType=Window}}" CommandParameter="OrderEntry" />
                <Button Content="Edit Job" Height="25" Width="100" Margin="5" Command="{Binding EditProductCommand}" />
                <Button Content="Delete Job" Height="25" Width="100" Margin="5" Command="{Binding DeleteProductCommand}" />
                <Button Content="Refresh" Height="25" Width="100" Margin="5" Command="{Binding RefreshCommand}" />
            </StackPanel>

            <!-- Filter Section -->
            <Border Grid.Row="1" BorderBrush="Gainsboro" BorderThickness="1" CornerRadius="4" Margin="5" Padding="5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="Job Number:" VerticalAlignment="Center" Margin="5,0" />
                    <TextBox Grid.Column="1" Text="{Binding FilterJobNumber, UpdateSourceTrigger=PropertyChanged}" Margin="5,2" />
                    <TextBlock Grid.Column="2" Text="Product Name:" VerticalAlignment="Center" Margin="5,0" />
                    <TextBox Grid.Column="3" Text="{Binding FilterProductName, UpdateSourceTrigger=PropertyChanged}" Margin="5,2" />
                    <TextBlock Grid.Column="4" Text="Rep:" VerticalAlignment="Center" Margin="5,0" />
                    <ComboBox Grid.Column="5" ItemsSource="{Binding RepList}" SelectedItem="{Binding FilterRep}" Margin="5,2" />
                    <TextBlock Grid.Column="6" Text="Urgent:" VerticalAlignment="Center" Margin="5,0" />
                    <ComboBox Grid.Column="7" SelectedItem="{Binding FilterUrgentStatus}" Margin="5,2" Width="70">
                        <sys:String>All</sys:String>
                        <sys:String>Yes</sys:String>
                        <sys:String>No</sys:String>
                    </ComboBox>
                    <Button Grid.Column="8" Content="Clear Filters" Command="{Binding ClearFiltersCommand}" Margin="10,2" Padding="10,2" />
                </Grid>
            </Border>

            <TextBlock Grid.Row="2" Text="Job List" Margin="5" />

            <!-- DataGrid and ProgressBar now share a Grid cell with a defined size -->
            <DataGrid Grid.Row="3" x:Name="ProductsDataGrid" Margin="5" ItemsSource="{Binding ProductsView}" SelectedItem="{Binding SelectedProduct, Mode=TwoWay}" AutoGenerateColumns="False" SelectionMode="Single" SelectionUnit="FullRow" CanUserAddRows="False">
                <DataGrid.Style>
                    <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Style>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Job Number" Binding="{Binding JobNumber}" />
                    <DataGridTextColumn Header="Product Name" Binding="{Binding ProductName}" />
                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" />
                    <DataGridTextColumn Header="Length" Binding="{Binding Length}" />
                    <DataGridTextColumn Header="Width" Binding="{Binding Width}" />
                    <DataGridTextColumn Header="Target Date" Binding="{Binding TargetDate, StringFormat=d}" />
                    <DataGridTextColumn Header="Job Type" Binding="{Binding JobType}" />
                    <DataGridTextColumn Header="Qty" Binding="{Binding Qty}" />
                    <DataGridTextColumn Header="SKU" Binding="{Binding SKU}" />
                    <DataGridTextColumn Header="Urgent" Binding="{Binding IsUrgent}" />
                    <DataGridTextColumn Header="Rep." Binding="{Binding Rep}" />
                </DataGrid.Columns>
            </DataGrid>

            <!-- Loading Overlay with ProgressBar. This is also in Grid.Row=3 -->
            <Border Grid.Row="3" Margin="5" Background="#80FFFFFF" Panel.ZIndex="1">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="Visibility" Value="Collapsed" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>
                <ProgressBar HorizontalAlignment="Center" VerticalAlignment="Center" IsIndeterminate="True" Width="200" Height="25" />
            </Border>
        </Grid>
    </Grid>
</UserControl>