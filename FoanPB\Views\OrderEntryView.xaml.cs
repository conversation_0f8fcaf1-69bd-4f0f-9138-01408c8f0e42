﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for OrderEntryView.xaml
    /// </summary>
    public partial class OrderEntryView : UserControl
    {
        public OrderEntryView()
        {
            InitializeComponent();
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {

        }

        private void CmbTemplate_GotFocus(object sender, RoutedEventArgs e)
        {

        }

        private void CmbTemplate_KeyDown(object sender, KeyEventArgs e)
        {

        }

        private void SaveTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            SaveTemplatePopup.IsOpen = true;
            txtTmpName.Focus();
        }

        private void SaveTemplateCancel_Click(object sender, RoutedEventArgs e)
        {
            SaveTemplatePopup.IsOpen = false;
        }
    }
}
