﻿<Window x:Class="FoanPB.Views.SplashScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Loading..." Height="250" Width="450"
        WindowStyle="None" ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Background="#FF3A3A3A" AllowsTransparency="True" ShowInTaskbar="False"
        BorderBrush="#FF505050" BorderThickness="1">
    <Grid>
        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
            <!-- You can add an Image here if you have a logo -->
            <!-- <Image Source="/YourProjectName;component/Assets/logo.png" Width="100" Height="100" Margin="0,0,0,20"/> -->
            <TextBlock Text="FOAN Production Book" FontSize="22" FontWeight="Bold" Margin="0,0,0,15" Foreground="White" HorizontalAlignment="Center"/>
            <TextBlock Text="Loading data, please wait..." FontSize="16" HorizontalAlignment="Center" Foreground="LightGray"/>
            <ProgressBar IsIndeterminate="True" Width="350" Height="20" Margin="0,25,0,0" Background="Gray" BorderThickness="0">
                <ProgressBar.Foreground>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FF2E86AB" Offset="0"/>
                        <GradientStop Color="#FF2ECC71" Offset="1"/>
                    </LinearGradientBrush>
                </ProgressBar.Foreground>
            </ProgressBar>
            <TextBlock Text="© Foan Professional Bannermakers" FontSize="10" Foreground="DarkGray" HorizontalAlignment="Center" Margin="0,30,0,0"/>
        </StackPanel>
    </Grid>
</Window>
