﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class StraightSewViewModel : StepTypeViewModelBase
    {
        protected override string TargetStepDescription => "Straight Sew";



        public StraightSewViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
        }

        // SelectedProduct override is no longer needed as base class handles the reset.

        public override ProductionStep SelectedProductionStep
        {
            get => base.SelectedProductionStep;
            set
            {
                base.SelectedProductionStep = value;
                if (base.SelectedProductionStep != null && IsValidStepForProcessing(base.SelectedProductionStep))
                {
                    // Pre-fill ViewModel's StartTime from step's actual StartTime for UI display
                    this.StartTime = base.SelectedProductionStep.StartTime;
                }
                // Base setter already handles InputTimeSpent and OnPropertyChanged
            }
        }

        protected override bool FilterSearchByDetail(object obj)
        {
            if (SelectedDetailFilter == "Operator")
            {
                if (obj is Product product)
                {
                    if (string.IsNullOrWhiteSpace(FilterDetailText)) return true; // No filter text, pass
                    string filterTextLower = FilterDetailText.ToLowerInvariant();
                    return product.ProductionSteps.Any(step =>
                        IsValidStepForProcessing(step) &&
                        !string.IsNullOrWhiteSpace(step.OperatorName) &&
                        step.OperatorName.ToLowerInvariant().Contains(filterTextLower));
                }
                return false; // Not a product
            }
            return base.FilterSearchByDetail(obj);
        }


    }
}
