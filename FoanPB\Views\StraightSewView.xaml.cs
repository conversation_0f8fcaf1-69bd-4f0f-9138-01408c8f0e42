﻿using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for StraightSewView.xaml
    /// </summary>
    public partial class StraightSewView : UserControl
    {
        public StraightSewView()
        {
            InitializeComponent();
        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                //Dispatcher.BeginInvoke(new Action(() =>
                //{
                //    var editedProductStep = e.Row.Item as ProductionStep;
                //    if (editedProductStep != null)
                //    {
                //        try
                //        {
                //            var dataAccess = new DataAccess();
                //            dataAccess.UpdateTimeSpent(editedProductStep);
                //        }
                //        catch (Exception ex)
                //        {
                //            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                //        }
                //    }
                //}), System.Windows.Threading.DispatcherPriority.Background);

                Dispatcher.BeginInvoke(async () =>
                {
                    var edited = e.Row.Item as ProductionStep;
                    if (edited != null)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}
