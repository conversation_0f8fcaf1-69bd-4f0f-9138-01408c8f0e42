﻿using FoanPB.Commands;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class StepTypePopupViewModel : BaseViewModel
    {
        private readonly SharedDataService _sharedDataService;
        private StepType _selectedStepType;
        private string _newStepDescription;

        /// <summary>
        /// Collection of StepType items to be displayed in the DataGrid.
        /// </summary>
        public ObservableCollection<StepType> StepTypes { get; }

        /// <summary>
        /// The currently selected StepType item in the DataGrid.
        /// </summary>
        public StepType SelectedStepType
        {
            get => _selectedStepType;
            set
            {
                if (_selectedStepType != value)
                {
                    _selectedStepType = value;
                    OnPropertyChanged(nameof(SelectedStepType));
                    // Update command states
                    //((RelayCommand)UpdateCommand).RaiseCanExecuteChanged();
                    //((RelayCommand)DeleteCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// The description of the new StepType to be added.
        /// </summary>
        public string NewStepDescription
        {
            get => _newStepDescription;
            set
            {
                if (_newStepDescription != value)
                {
                    _newStepDescription = value;
                    OnPropertyChanged(nameof(NewStepDescription));
                    // Update AddCommand state
                    //((RelayCommand)AddCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Command to add a new StepType.
        /// </summary>
        public ICommand AddCommand { get; }

        /// <summary>
        /// Command to update the selected StepType.
        /// </summary>
        public ICommand UpdateCommand { get; }

        /// <summary>
        /// Command to delete the selected StepType.
        /// </summary>
        public ICommand DeleteCommand { get; }

        /// <summary>
        /// Command to close the StepType dialog.
        /// (Optional: Not bound in current XAML but can be used if needed)
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Event to request the closing of the StepType dialog window.
        /// </summary>
        public event Action RequestClose;

        /// <summary>
        /// Initializes a new instance of the StepTypePopupViewModel.
        /// </summary>
        /// <param name="sharedDataService">The shared data service for data operations.</param>
        public StepTypePopupViewModel(SharedDataService sharedDataService)
        {
            _sharedDataService = sharedDataService ?? throw new ArgumentNullException(nameof(sharedDataService));
            StepTypes = _sharedDataService.StepTypes;

            // Initialize commands with corresponding methods and can-execute predicates
            AddCommand = new RelayCommand(AddStepType, CanAddStepType);
            UpdateCommand = new RelayCommand(UpdateStepType, CanUpdateStepType);
            DeleteCommand = new RelayCommand(DeleteStepType, CanDeleteStepType);
            CloseCommand = new RelayCommand(CloseWindow);
        }

        /// <summary>
        /// Adds a new StepType to the collection and database.
        /// </summary>
        private void AddStepType()
        {
            if (string.IsNullOrWhiteSpace(NewStepDescription))
                return;

            try
            {
                var newStepType = new StepType
                {
                    StepDescription = NewStepDescription.Trim()
                };
                _sharedDataService.AddStepType(newStepType);
                NewStepDescription = string.Empty; // Clear the input after adding
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error adding step type: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to add new step type.", ex);
            }
        }

        /// <summary>
        /// Determines whether a new StepType can be added.
        /// </summary>
        /// <returns>True if NewStepDescription is not empty or whitespace; otherwise, false.</returns>
        private bool CanAddStepType()
        {
            return !string.IsNullOrWhiteSpace(NewStepDescription);
        }

        /// <summary>
        /// Updates the selected StepType in the collection and database.
        /// </summary>
        private void UpdateStepType()
        {
            if (SelectedStepType == null)
                return;

            try
            {
                _sharedDataService.UpdateStepType(SelectedStepType);
                // Optionally, provide feedback to the user
                // MessageBox.Show("StepType updated successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error updating step type: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to update step type.", ex);
            }
        }

        /// <summary>
        /// Determines whether the selected StepType can be updated.
        /// </summary>
        /// <returns>True if a StepType is selected; otherwise, false.</returns>
        private bool CanUpdateStepType()
        {
            return SelectedStepType != null;
        }

        /// <summary>
        /// Deletes the selected StepType from the collection and database.
        /// </summary>
        private void DeleteStepType()
        {
            if (SelectedStepType == null)
                return;

            try
            {
                // Optionally, confirm deletion with the user
                // var result = MessageBox.Show($"Are you sure you want to delete '{SelectedStepType.StepDescription}'?", "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                // if (result != MessageBoxResult.Yes)
                //     return;

                _sharedDataService.DeleteStepType(SelectedStepType.TypeId);
                SelectedStepType = null; // Clear the selection after deletion
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error deleting step type: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to delete step type.", ex);
            }
        }

        /// <summary>
        /// Determines whether the selected StepType can be deleted.
        /// </summary>
        /// <returns>True if a StepType is selected; otherwise, false.</returns>
        private bool CanDeleteStepType()
        {
            return SelectedStepType != null;
        }

        /// <summary>
        /// Requests the closing of the StepType dialog window.
        /// </summary>
        private void CloseWindow()
        {
            RequestClose?.Invoke();
        }
    }

}
