﻿using Dapper;
using FoanPB.Models;
using System.Data;
using System.Data.OleDb;

namespace FoanPB.DataService
{
    public class OperatorService
    {
        private readonly string _connectionString;

        public OperatorService(string connectionString)
        {
            _connectionString = connectionString;
        }

        public IEnumerable<Employee> GetAllOperators()
        {
            using (IDbConnection db = new OleDbConnection(_connectionString))
            {
                return db.Query<Employee>("SELECT * FROM tblEmployees").ToList();
            }
        }


        public IEnumerable<Department> GetOperatorDepartments()
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                // Ensure the connection is open
                connection.Open();

                // SQL query to fetch distinct DepartmentID and DepartmentName
                string query = @"
                SELECT DISTINCT DepartmentID, DepartmentName 
                FROM tblDepartment";

                try
                {
                    // Execute the query using Dapper and map results to Department objects
                    var departments = connection.Query<Department>(query).ToList();
                    return departments;
                }
                catch (OleDbException ex)
                {
                    // Handle database-related exceptions
                    // Log the exception as needed
                    throw new ApplicationException("An error occurred while fetching operator departments.", ex);
                }
                catch (Exception ex)
                {
                    // Handle other possible exceptions
                    // Log the exception as needed
                    throw new ApplicationException("An unexpected error occurred.", ex);
                }
            }
        }

        public int AddOperator(Employee newOperator)
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        var sql = "INSERT INTO tblEmployees (EmpName, EmpDepartmentID, EmpEmail, EmpPhone, IsActive) VALUES (@EmpName, @EmpDepartmentID, @EmpEmail, @EmpPhone, @IsActive)";
                        //connection.Execute(sql, newOperator, transaction);
                        connection.Execute(sql, new
                        {
                            newOperator.EmpName,
                            newOperator.EmpDepartmentID,
                            newOperator.EmpEmail,
                            newOperator.EmpPhone,
                            newOperator.IsActive
                        }, transaction);

                        var id = connection.QuerySingle<int>("SELECT @@IDENTITY", transaction: transaction);
                        transaction.Commit();
                        return id;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        public void UpdateOperator(Employee newOperator)
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                var sql = "UPDATE tblEmployees SET EmpName = @EmpName, EmpDepartmentID = @EmpDepartmentID, EmpEmail = @EmpEmail, EmpPhone = @EmpPhone, IsActive = @IsActive WHERE EmpId = @EmpId";

                connection.Open();
                var affectedRows = connection.Execute(sql, new
                {
                    newOperator.EmpName,
                    newOperator.EmpDepartmentID,
                    newOperator.EmpEmail,
                    newOperator.EmpPhone,
                    newOperator.IsActive,
                    newOperator.EmpId
                });

            }
        }

        

        public void DeleteOperator(int operatorId)
        {
            using (IDbConnection db = new OleDbConnection(_connectionString))
            {
                db.Execute("DELETE FROM tblEmployees WHERE EmpId = @EmpId", new { EmpId = operatorId });
            }
        }
    }
}
