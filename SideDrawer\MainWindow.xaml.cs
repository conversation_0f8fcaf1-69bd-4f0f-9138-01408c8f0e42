﻿using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace SideDrawer
{
    public partial class MainWindow : Window
    {
        private bool isDrawerOpen = false;

        public MainWindow()
        {
            InitializeComponent();
        }

        private void ToggleDrawer_Click(object sender, RoutedEventArgs e)
        {
            if (isDrawerOpen)
            {
                CloseDrawer();
            }
            else
            {
                OpenDrawer();
            }
            isDrawerOpen = !isDrawerOpen;
        }

        private void OpenDrawer()
        {
            var slideInAnimation = new DoubleAnimation
            {
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideInAnimation);
        }

        private void CloseDrawer()
        {
            var slideOutAnimation = new DoubleAnimation
            {
                To = SideDrawer.Width, // Slide it completely out of view
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideOutAnimation);
        }
    }
}