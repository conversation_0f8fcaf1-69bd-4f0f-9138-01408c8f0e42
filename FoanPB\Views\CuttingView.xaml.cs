using FoanPB.DataService;
using FoanPB.Models;
using FoanPB.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for CuttingView.xaml
    /// </summary>
    public partial class CuttingView : UserControl
    {
        public CuttingView()
        {
            InitializeComponent();

            // Subscribe to SideDrawer visibility changes for animation
            this.Loaded += CuttingView_Loaded;
        }

        private void CuttingView_Loaded(object sender, RoutedEventArgs e)
        {
            if (this.DataContext is CuttingViewModel viewModel)
            {
                viewModel.TimeLogSideDrawerViewModel.PropertyChanged += TimeLogSideDrawerViewModel_PropertyChanged;
            }
        }

        private void TimeLogSideDrawerViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TimeLogSideDrawerViewModel.IsVisible))
            {
                var viewModel = sender as TimeLogSideDrawerViewModel;
                if (viewModel != null)
                {
                    if (viewModel.IsVisible)
                    {
                        OpenDrawer();
                    }
                    else
                    {
                        CloseDrawer();
                    }
                }
            }
        }

        private void OpenDrawer()
        {
            var slideInAnimation = new DoubleAnimation
            {
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideInAnimation);
        }

        private void CloseDrawer()
        {
            var slideOutAnimation = new DoubleAnimation
            {
                To = 450,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideOutAnimation);
        }

        private void SideDrawerOverlay_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Close drawer when clicking on overlay background
            if (this.DataContext is CuttingViewModel viewModel)
            {
                viewModel.TimeLogSideDrawerViewModel.IsVisible = false;
            }
        }


        private void EndTime_Click(object sender, RoutedEventArgs e)
        {
            //txtEndTime.Text = DateTime.Now.ToString("HH:mm");
        }

        private void Search_Click(object sender, RoutedEventArgs e)
        {

        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                Dispatcher.BeginInvoke(async () =>
                {
                    var edited = e.Row.Item as ProductionStep;
                    if (edited != null)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);

                            // Notify that the ProductionStep TimeSpent has changed, which may affect Product.IsCompleted
                            if (this.DataContext is ViewModels.CuttingViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}
