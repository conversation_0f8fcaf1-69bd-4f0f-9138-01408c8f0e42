using FoanPB.DataService;
using FoanPB.Models;
using FoanPB.ViewModels;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
namespace FoanPB.Views
{
    public partial class CuttingView : UserControl
    {
        public CuttingView()
        {
            InitializeComponent();
            this.Loaded += CuttingView_Loaded;
        }
        private void CuttingView_Loaded(object sender, RoutedEventArgs e)
        {
            if (this.DataContext is CuttingViewModel viewModel)
            {
                viewModel.TimeLogSideDrawerViewModel.PropertyChanged += TimeLogSideDrawerViewModel_PropertyChanged;
            }
        }
        private void TimeLogSideDrawerViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TimeLogSideDrawerViewModel.IsVisible))
            {
                var viewModel = sender as TimeLogSideDrawerViewModel;
                if (viewModel != null)
                {
                    if (viewModel.IsVisible)
                    {
                        OpenDrawer();
                    }
                    else
                    {
                        CloseDrawer();
                    }
                }
            }
        }
        private void OpenDrawer()
        {
            var slideInAnimation = new DoubleAnimation
            {
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideInAnimation);
        }
        private void CloseDrawer()
        {
            var slideOutAnimation = new DoubleAnimation
            {
                To = 450,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            DrawerTransform.BeginAnimation(TranslateTransform.XProperty, slideOutAnimation);
        }
        private void SideDrawerOverlay_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.OriginalSource == SideDrawerOverlay)
            {
                if (this.DataContext is CuttingViewModel viewModel)
                {
                    viewModel.TimeLogSideDrawerViewModel.IsVisible = false;
                }
            }
        }
        private void EndTime_Click(object sender, RoutedEventArgs e)
        {
        }
        private void Search_Click(object sender, RoutedEventArgs e)
        {
        }
        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                Dispatcher.BeginInvoke(async () =>
                {
                    var edited = e.Row.Item as ProductionStep;
                    if (edited != null)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);
                            if (this.DataContext is ViewModels.CuttingViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}