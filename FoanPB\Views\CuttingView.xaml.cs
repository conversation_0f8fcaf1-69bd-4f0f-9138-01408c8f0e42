using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for CuttingView.xaml
    /// </summary>
    public partial class CuttingView : UserControl
    {
        public CuttingView()
        {
            InitializeComponent();
        }

        private void StartTime_Click(object sender, RoutedEventArgs e)
        {
            //txtStartTime.Text = DateTime.Now.ToString("HH:mm");
        }
        private void EndTime_Click(object sender, RoutedEventArgs e)
        {
            //txtEndTime.Text = DateTime.Now.ToString("HH:mm");
        }

        private void Search_Click(object sender, RoutedEventArgs e)
        {

        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                Dispatcher.BeginInvoke(async () =>
                {
                    var edited = e.Row.Item as ProductionStep;
                    if (edited != null)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);

                            // Notify that the ProductionStep TimeSpent has changed, which may affect Product.IsCompleted
                            if (this.DataContext is ViewModels.CuttingViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}
