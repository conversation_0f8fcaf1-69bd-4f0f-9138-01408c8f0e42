using System.Windows;
using System.Windows.Controls;

namespace FoanPB.Data
{
    /// <summary>
    /// Boxed value types for improved efficiency
    /// </summary>
    internal static class ValueBoxes
    {
        internal static object TrueBox = true;
        internal static object FalseBox = false;
        internal static object VerticalBox = Orientation.Vertical;
        internal static object HorizontalBox = Orientation.Horizontal;
        internal static object VisibleBox = Visibility.Visible;
        internal static object CollapsedBox = Visibility.Collapsed;
        internal static object HiddenBox = Visibility.Hidden;
        internal static object Double01Box = .1;
        internal static object Double0Box = .0;
        internal static object Double1Box = 1.0;
        internal static object Double10Box = 10.0;
        internal static object Double20Box = 20.0;
        internal static object Double100Box = 100.0;

        /// <summary>
        /// Returns a boxed boolean value
        /// </summary>
        /// <param name="value">The boolean value to box</param>
        /// <returns>Boxed boolean value</returns>
        internal static object BooleanBox(bool value) => value ? TrueBox : FalseBox;
    }
}
