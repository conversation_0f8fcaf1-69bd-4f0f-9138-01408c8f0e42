﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace SideDrawer.ViewModels
{
    // Model class for a single time log entry
    public class TimeLogEntry
    {
        public string UserName { get; set; }
        public string TimeLogged { get; set; }
        public DateTime Timestamp { get; set; }
        public string UserAvatarUrl { get; set; } // For simplicity, we can use initials
        public string UserInitials => string.IsNullOrEmpty(UserName) ? "" : $"{UserName[0]}";
    }

    public class TaskDetailViewModel : INotifyPropertyChanged
    {
        private string _taskTitle;
        public string TaskTitle
        {
            get => _taskTitle;
            set { _taskTitle = value; OnPropertyChanged(); }
        }

        private string _selectedStatus;
        public string SelectedStatus
        {
            get => _selectedStatus;
            set { _selectedStatus = value; OnPropertyChanged(); }
        }

        public ObservableCollection<string> StatusOptions { get; }
        public ObservableCollection<TimeLogEntry> TimeLogs { get; }

        public string TotalTimeLogged => "8h 30m"; // Example static value

        public TaskDetailViewModel()
        {
            TaskTitle = "Design new user onboarding flow";
            StatusOptions = new ObservableCollection<string> { "To do", "In progress", "Done" };
            SelectedStatus = "In progress";

            TimeLogs = new ObservableCollection<TimeLogEntry>
            {
                new TimeLogEntry { UserName = "Jane Doe", TimeLogged = "3h 30m", Timestamp = DateTime.Now.AddHours(-2) },
                new TimeLogEntry { UserName = "John Smith", TimeLogged = "5h 00m", Timestamp = DateTime.Now.AddDays(-1) }
            };

            LogTimeCommand = new RelayCommand(p => { /* Logic to log time */ });
            CancelCommand = new RelayCommand(p => { /* Logic to cancel */ });
        }

        public ICommand LogTimeCommand { get; }
        public ICommand CancelCommand { get; }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}