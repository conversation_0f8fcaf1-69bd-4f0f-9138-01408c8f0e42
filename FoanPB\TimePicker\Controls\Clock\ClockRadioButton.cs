using System.Windows;
using System.Windows.Controls;

namespace FoanPB.Controls.Clock
{
    /// <summary>
    /// A specialized RadioButton used in clock controls
    /// </summary>
    public class ClockRadioButton : RadioButton
    {
        static ClockRadioButton()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ClockRadioButton), new FrameworkPropertyMetadata(typeof(ClockRadioButton)));
        }
        /// <summary>
        /// Gets or sets the numeric value associated with this radio button
        /// </summary>
        public int Num { get; set; }
    }
}
