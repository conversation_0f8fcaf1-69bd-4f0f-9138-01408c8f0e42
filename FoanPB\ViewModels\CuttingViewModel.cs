using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class CuttingViewModel : StepTypeViewModelBase
    {
        protected override string TargetStepDescription => "Cutting";

        private string _startTimeText; // Specific to CuttingViewModel for its UI
        public string StartTimeText
        {
            get => _startTimeText;
            set
            {
                _startTimeText = value;
                OnPropertyChanged(nameof(StartTimeText));
            }
        }

        // TimeLog SideDrawer ViewModel
        private TimeLogSideDrawerViewModel _timeLogSideDrawerViewModel;
        public TimeLogSideDrawerViewModel TimeLogSideDrawerViewModel
        {
            get => _timeLogSideDrawerViewModel;
            set
            {
                _timeLogSideDrawerViewModel = value;
                OnPropertyChanged(nameof(TimeLogSideDrawerViewModel));
            }
        }

        public ICommand UpdateToCurrentDateCommand { get; }

        public CuttingViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
            UpdateToCurrentDateCommand = new RelayCommand(UpdateToCurrentDate);

            // Initialize TimeLog SideDrawer ViewModel with dependencies
            TimeLogSideDrawerViewModel = new TimeLogSideDrawerViewModel(dataAccess, sharedDataService);
        }

        private void UpdateToCurrentDate()
        {
            // This method in original CuttingViewModel sets a specific StartTimeText property
            StartTimeText = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        }

        protected override void ExecuteTimeLogProcess(ProductionStep step)
        {
            if (!CanTimeLogProcess(step)) return;

            // Set the selected production step to enable time logging functionality
            SelectedProductionStep = step;

            // Show the SideDrawer for time logging
            TimeLogSideDrawerViewModel.ShowForProductionStep(step);
        }

        protected override bool FilterSearchByDetail(object obj)
        {
            if (SelectedDetailFilter == "Equipment")
            {
                if (obj is Product product)
                {
                    if (string.IsNullOrWhiteSpace(FilterDetailText)) return true; // No filter text, pass
                    string filterTextLower = FilterDetailText.ToLowerInvariant();
                    // Original CuttingViewModel logic: checks equipment in ANY step.
                    return product.ProductionSteps.Any(step =>
                        !string.IsNullOrWhiteSpace(step.EquipmentName) &&
                        step.EquipmentName.ToLowerInvariant().Contains(filterTextLower));
                }
                return false; // Not a product
            }
            return base.FilterSearchByDetail(obj);
        }
    }
}
