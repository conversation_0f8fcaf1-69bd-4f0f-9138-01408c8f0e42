using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public class CuttingViewModel : StepTypeViewModelBase
    {
        protected override string TargetStepDescription => "Cutting";

        private string _startTimeText; // Specific to CuttingViewModel for its UI
        public string StartTimeText
        {
            get => _startTimeText;
            set
            {
                _startTimeText = value;
                OnPropertyChanged(nameof(StartTimeText));
            }
        }

        public ICommand UpdateToCurrentDateCommand { get; }

        public CuttingViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
            : base(sharedDataService, dataAccess)
        {
            UpdateToCurrentDateCommand = new RelayCommand(UpdateToCurrentDate);
        }

        private void UpdateToCurrentDate()
        {
            // This method in original CuttingViewModel sets a specific StartTimeText property
            StartTimeText = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        }

        protected override bool FilterSearchByDetail(object obj)
        {
            if (SelectedDetailFilter == "Equipment")
            {
                if (obj is Product product)
                {
                    if (string.IsNullOrWhiteSpace(FilterDetailText)) return true; // No filter text, pass
                    string filterTextLower = FilterDetailText.ToLowerInvariant();
                    // Original CuttingViewModel logic: checks equipment in ANY step.
                    return product.ProductionSteps.Any(step =>
                        !string.IsNullOrWhiteSpace(step.EquipmentName) &&
                        step.EquipmentName.ToLowerInvariant().Contains(filterTextLower));
                }
                return false; // Not a product
            }
            return base.FilterSearchByDetail(obj);
        }
    }
}
