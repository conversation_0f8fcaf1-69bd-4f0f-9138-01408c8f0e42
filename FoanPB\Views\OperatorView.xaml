﻿<UserControl x:Class="FoanPB.Views.OperatorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:FoanPB.Views"
             xmlns:Dpt="clr-namespace:FoanPB.Service"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <!-- Register the converter -->
        <Dpt:DepartmentIDToNameMultiConverter x:Key="DeptIDToNameConverter" />
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <DockPanel Grid.Row="0">
            <Menu DockPanel.Dock="Top">
                <MenuItem Header="Department" Command="{Binding OpenDepartmentPopupCommand}"/>
            </Menu>
            <!-- Other UI components can go here -->
        </DockPanel>
        <StackPanel Grid.Row="1" Margin="10">
        <ListView ItemsSource="{Binding Operators}" SelectedItem="{Binding SelectedOperator}" IsEnabled="{Binding IsListEnabled}" HorizontalAlignment="Stretch">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Edit" Width="75">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="Edit" Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <!--<GridViewColumn Header="ID" DisplayMemberBinding="{Binding OperatorId}" Width="0"/>-->

                        <GridViewColumn Header="Name" DisplayMemberBinding="{Binding EmpName}" Width="150"/>
                        <GridViewColumn Header="Department" Width="150">
                            <GridViewColumn.DisplayMemberBinding>
                                <MultiBinding Converter="{StaticResource DeptIDToNameConverter}">
                                    <Binding Path="EmpDepartmentID" />
                                    <Binding Path="DataContext.OperatorDepartments" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                </MultiBinding>
                            </GridViewColumn.DisplayMemberBinding>
                        </GridViewColumn>
                        <GridViewColumn Header="Email" DisplayMemberBinding="{Binding EmpEmail}" Width="200"/>
                    <GridViewColumn Header="Phone" DisplayMemberBinding="{Binding EmpPhone}" Width="150"/>
                    <GridViewColumn Header="Active" DisplayMemberBinding="{Binding IsActive}" Width="50"/>
                    
                    <GridViewColumn Header="Delete" Width="75">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="Delete" Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <StackPanel Orientation="Horizontal">
            <TextBlock Text="Name:" IsEnabled="{Binding IsDetailEnabled}"/>
            <TextBox Text="{Binding SelectedOperator.EmpName}" IsEnabled="{Binding IsDetailEnabled}" Width="120" Height="30" Margin="5"/>
            <TextBlock Text="Department:" IsEnabled="{Binding IsDetailEnabled}"/>
                <ComboBox
                    ItemsSource="{Binding OperatorDepartments}"
                    DisplayMemberPath="DepartmentName"
                    SelectedValuePath="DepartmentID"
                    SelectedValue="{Binding SelectedOperator.EmpDepartmentID, Mode=TwoWay}"
                    IsEnabled="{Binding IsDetailEnabled}"
                    Width="120"
                    Height="30"
                    Margin="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"/>
                <TextBlock Text="Email:" IsEnabled="{Binding IsDetailEnabled}"/>
            <TextBox Text="{Binding SelectedOperator.EmpEmail}" IsEnabled="{Binding IsDetailEnabled}" Width="120" Height="30" Margin="5"/>
            <TextBlock Text="Phone:" IsEnabled="{Binding IsDetailEnabled}"/>
            <TextBox Text="{Binding SelectedOperator.EmpPhone}" IsEnabled="{Binding IsDetailEnabled}" Width="120" Height="30" Margin="5"/>
            
            <CheckBox IsChecked="{Binding SelectedOperator.IsActive}" IsEnabled="{Binding IsDetailEnabled}" Content="Is Active" Margin="5"/>
        </StackPanel>

        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button Content="Add" Command="{Binding AddCommand}" Width="100" Margin="5" IsEnabled="{Binding IsAddEnabled}"/>
            <Button Content="Cancel" Command="{Binding UndoCommand}" IsEnabled="{Binding IsDetailEnabled}" Width="100" Margin="5"/>
            <Button Content="Save" Command="{Binding SaveCommand}" IsEnabled="{Binding IsDetailEnabled}" Width="100" Margin="5"/>
        </StackPanel>
</StackPanel>
    </Grid>
</UserControl>
