﻿<UserControl x:Class="SideDrawer.Controls.TimePicker"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="32" d:DesignWidth="150">
    <Grid>
        <!-- This ToggleButton acts as the display and the trigger for the Popup -->
        <ToggleButton x:Name="DisplayButton" IsChecked="False">
            <ToggleButton.Style>
                <Style TargetType="ToggleButton">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                    <Setter Property="BorderThickness" Value="1"/>
                    <Setter Property="Padding" Value="8,5"/>
                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ToggleButton">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        SnapsToDevicePixels="True">
                                    <ContentPresenter Margin="{TemplateBinding Padding}"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </ToggleButton.Style>
        </ToggleButton>

        <!-- The Popup for selecting time -->
        <Popup x:Name="TimePopup"
               IsOpen="{Binding IsChecked, ElementName=DisplayButton, Mode=TwoWay}"
               StaysOpen="False"
               Placement="Bottom"
               PlacementTarget="{Binding ElementName=DisplayButton}">

            <Border Background="{StaticResource BackgroundBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    CornerRadius="3"
                    Padding="5">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="8" Color="Black" Opacity="0.15"/>
                </Border.Effect>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Hour Selector -->
                    <ListBox x:Name="HourList" Grid.Column="0" Width="50" MaxHeight="150"
                             SelectionChanged="TimeSelectionChanged"/>
                    <!-- Minute Selector -->
                    <ListBox x:Name="MinuteList" Grid.Column="1" Width="50" MaxHeight="150"
                             SelectionChanged="TimeSelectionChanged"/>
                    <!-- AM/PM Selector -->
                    <ListBox x:Name="AmPmList" Grid.Column="2" Width="50" MaxHeight="150"
                             SelectionChanged="TimeSelectionChanged"/>
                </Grid>
            </Border>
        </Popup>
    </Grid>
</UserControl>