﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace FoanPB.Commands
{
   

    public class RelayCommand : ICommand
    {
        private readonly Func<object, Task> _executeAsyncWithParam;
        private readonly Func<Task> _executeAsync;
        private readonly Action<object> _executeWithParam;
        private readonly Action _execute;
        private readonly Predicate<object> _canExecuteWithParam;
        private readonly Func<bool> _canExecute;

        /// <summary>
        /// Event raised when the ability of the command to execute changes.
        /// </summary>
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        #region Constructors

        /// <summary>
        /// Constructor for asynchronous commands with parameter.
        /// </summary>
        /// <param name="executeAsync">The asynchronous action to execute.</param>
        /// <param name="canExecute">Predicate to determine if the command can execute.</param>
        public RelayCommand(Func<object, Task> executeAsync, Predicate<object> canExecute = null)
        {
            _executeAsyncWithParam = executeAsync ?? throw new ArgumentNullException(nameof(executeAsync));
            _canExecuteWithParam = canExecute;
        }

        /// <summary>
        /// Constructor for asynchronous commands without parameter.
        /// </summary>
        /// <param name="executeAsync">The asynchronous action to execute.</param>
        /// <param name="canExecute">Predicate to determine if the command can execute.</param>
        public RelayCommand(Func<Task> executeAsync, Func<bool> canExecute = null)
        {
            _executeAsync = executeAsync ?? throw new ArgumentNullException(nameof(executeAsync));
            _canExecute = canExecute;
        }

        /// <summary>
        /// Constructor for synchronous commands with parameter.
        /// </summary>
        /// <param name="execute">The action to execute.</param>
        /// <param name="canExecute">Predicate to determine if the command can execute.</param>
        public RelayCommand(Action<object> execute, Predicate<object> canExecute = null)
        {
            _executeWithParam = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecuteWithParam = canExecute;
        }

        /// <summary>
        /// Constructor for synchronous commands without parameter.
        /// </summary>
        /// <param name="execute">The action to execute.</param>
        /// <param name="canExecute">Predicate to determine if the command can execute.</param>
        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        #endregion

        /// <inheritdoc />
        public bool CanExecute(object parameter)
        {
            if (_canExecuteWithParam != null)
            {
                return _canExecuteWithParam(parameter);
            }
            else if (_canExecute != null)
            {
                return _canExecute();
            }
            return true;
        }

        /// <inheritdoc />
        public async void Execute(object parameter)
        {
            if (_executeAsyncWithParam != null)
            {
                try
                {
                    await _executeAsyncWithParam(parameter);
                }
                catch (Exception ex)
                {
                    // Handle exceptions appropriately (e.g., log them)
                    MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (_executeAsync != null)
            {
                try
                {
                    await _executeAsync();
                }
                catch (Exception ex)
                {
                    // Handle exceptions appropriately (e.g., log them)
                    MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (_executeWithParam != null)
            {
                try
                {
                    _executeWithParam(parameter);
                }
                catch (Exception ex)
                {
                    // Handle exceptions appropriately (e.g., log them)
                    MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (_execute != null)
            {
                try
                {
                    _execute();
                }
                catch (Exception ex)
                {
                    // Handle exceptions appropriately (e.g., log them)
                    MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                throw new InvalidOperationException("No execute action is defined.");
            }
        }
    }

    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool> _canExecute;

        public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute((T)parameter);
        }

        public void Execute(object parameter)
        {
            _execute((T)parameter);
        }
    }
}
