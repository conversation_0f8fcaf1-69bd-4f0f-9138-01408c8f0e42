﻿using Dapper;
using FoanPB.Models;
using System.Data;
using System.Data.OleDb;
namespace FoanPB.DataService
{
    public class TemplateService
    {
        private readonly string _connectionString;
        public TemplateService(string connectionString)
        {
            _connectionString = connectionString;
        }
        public IEnumerable<ProductionTemplate> GetAllTemplatesWithSteps()
        {
            using (IDbConnection db = new OleDbConnection(_connectionString))
            {
                string sql = @"
                SELECT 
                    t.TemplateId, 
                    t.TemplateName, 
                    t.TemplateDescription, 
                    t.ProductName, 
                    t.Description, 
                    t.SKU,
                    s.StepId,
                    s.TemplateId AS StepTemplateId,
                    s.StepNumber,
                    s.StepDescription,
                    s.EquipmentName,
                    s.Estmin
                FROM tblProductTemplates t
                LEFT JOIN tblProductionStepsTmp s ON t.TemplateId = s.TemplateId
                ORDER BY t.TemplateId, s.StepNumber
            ";
                var templateDictionary = new Dictionary<int, ProductionTemplate>();
                var templates = db.Query<ProductionTemplate, ProductionStepTemplate, ProductionTemplate>(
                    sql,
                    (template, step) =>
                    {
                        if (!templateDictionary.TryGetValue(template.TemplateId, out var currentTemplate))
                        {
                            currentTemplate = template;
                            currentTemplate.ProductionStepTemplates = new List<ProductionStepTemplate>();
                            templateDictionary.Add(currentTemplate.TemplateId, currentTemplate);
                        }
                        if (step != null && step.StepId != 0)
                        {
                            step.TemplateId = currentTemplate.TemplateId;
                            currentTemplate.ProductionStepTemplates.Add(step);
                        }
                        return currentTemplate;
                    },
                    splitOn: "StepId"
                )
                .Distinct()
                .ToList();
                return templates;
            }
        }
        public async Task<IEnumerable<ProductionTemplate>> GetAllTemplatesWithStepsAsync()
        {
            using (IDbConnection db = new OleDbConnection(_connectionString))
            {
                string sql = @"
                SELECT 
                    t.TemplateId, 
                    t.TemplateName, 
                    t.TemplateDescription, 
                    t.ProductName, 
                    t.Description,
                    t.[Length],
                    t.[Width],
                    t.SKU,
                    s.StepId,
                    s.TemplateId AS StepTemplateId,
                    s.StepNumber,
                    s.StepDescription,
                    s.EquipmentName,
                    s.Estmin
                FROM tblProductTemplates t
                LEFT JOIN tblProductionStepsTmp s ON t.TemplateId = s.TemplateId
                ORDER BY t.TemplateId, s.StepNumber
            ";
                var templateDictionary = new Dictionary<int, ProductionTemplate>();
                var templates = await db.QueryAsync<ProductionTemplate, ProductionStepTemplate, ProductionTemplate>(
                    sql,
                    (template, step) =>
                    {
                        if (!templateDictionary.TryGetValue(template.TemplateId, out var currentTemplate))
                        {
                            currentTemplate = template;
                            currentTemplate.ProductionStepTemplates = new List<ProductionStepTemplate>();
                            templateDictionary.Add(currentTemplate.TemplateId, currentTemplate);
                        }
                        if (step != null && step.StepId != 0)
                        {
                            step.TemplateId = currentTemplate.TemplateId;
                            currentTemplate.ProductionStepTemplates.Add(step);
                        }
                        return currentTemplate;
                    },
                    splitOn: "StepId"
                );
                return templates.Distinct().ToList();
            }
        }
        public ProductionTemplate AddTemplate(Product product, string templateName, string templateDescription)
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        var insertTemplateSql = @"INSERT INTO tblProductTemplates 
                    (TemplateName, TemplateDescription, ProductName, Description, [Length], [Width], [SKU]) 
                    VALUES (@TemplateName, @TemplateDescription, @ProductName, @Description, @Length, @Width, @SKU)";
                        connection.Execute(
                            insertTemplateSql,
                            new
                            {
                                TemplateName = templateName,
                                TemplateDescription = templateDescription,
                                ProductName = product.ProductName,
                                Description = product.Description,
                                Length = product.Length,
                                Width = product.Width,
                                SKU = product.SKU,
                            },
                            transaction
                        );
                        var templateId = connection.ExecuteScalar<int>(
                            "SELECT @@IDENTITY",
                            transaction: transaction
                        );
                        foreach (var step in product.ProductionSteps)
                        {
                            var insertStepSql = @"INSERT INTO tblProductionStepsTmp 
                        (TemplateId, StepNumber, StepDescription, EquipmentName, Estmin) 
                        VALUES (@TemplateId, @StepNumber, @StepDescription, @EquipmentName, @Estmin)";
                            connection.Execute(
                                insertStepSql,
                                new
                                {
                                    TemplateId = templateId,
                                    StepNumber = step.StepNumber,
                                    StepDescription = step.StepDescription,
                                    EquipmentName = step.EquipmentName,
                                    Estmin = step.Estmin
                                },
                                transaction
                            );
                        }
                        transaction.Commit();
                        return new ProductionTemplate
                        {
                            TemplateId = templateId,
                            TemplateName = templateName,
                            TemplateDescription = templateDescription,
                            ProductName = product.ProductName,
                            Description = product.Description,
                            SKU = product.SKU,
                            ProductionStepTemplates = product.ProductionSteps.Select(step => new ProductionStepTemplate
                            {
                                TemplateId = templateId,
                                StepNumber = step.StepNumber,
                                StepDescription = step.StepDescription,
                                EquipmentName = step.EquipmentName,
                                Estmin = step.Estmin ?? 0
                            }).ToList()
                        };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception($"Failed to save template: {ex.Message}", ex);
                    }
                }
            }
        }
        public Product LoadTemplate(string templateName)
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                connection.Open();
                var template = connection.QuerySingleOrDefault<Product>(
                    @"SELECT TemplateName, TemplateDescription, ProductName, Description, SKU
                  FROM tblProductTemplates 
                  WHERE TemplateName = @TemplateName",
                    new { TemplateName = templateName });
                if (template != null)
                {
                    var steps = connection.Query<ProductionStep>(
                        @"SELECT StepId, TemplateId, StepNumber, StepDescription, EquipmentName, Estmin
                      FROM tblProductionStepsTmp 
                      WHERE TemplateId = (SELECT TemplateId FROM tblProductTemplates WHERE TemplateName = @TemplateName)",
                        new { TemplateName = templateName }).ToList();
                    template.ProductionSteps = steps.Select(step => new ProductionStep
                    {
                        StepId = step.StepId,
                        StepNumber = step.StepNumber,
                        StepDescription = step.StepDescription,
                        Estmin = step.Estmin
                    }).ToList();
                }
                return template;
            }
        }
        public async Task DeleteTemplateAsync(int templateId)
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        var deleteStepsSql = "DELETE FROM tblProductionStepsTmp WHERE TemplateId = @TemplateId";
                        await connection.ExecuteAsync(
                            deleteStepsSql,
                            new { TemplateId = templateId },
                            transaction
                        );
                        var deleteTemplateSql = "DELETE FROM tblProductTemplates WHERE TemplateId = @TemplateId";
                        int rowsAffected = await connection.ExecuteAsync(
                            deleteTemplateSql,
                            new { TemplateId = templateId },
                            transaction
                        );
                        if (rowsAffected == 0)
                        {
                            throw new Exception("No template found with the provided TemplateId.");
                        }
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception($"Failed to delete template: {ex.Message}", ex);
                    }
                }
            }
        }
        public List<string> GetTemplateNames()
        {
            using (var connection = new OleDbConnection(_connectionString))
            {
                connection.Open();
                return connection.Query<string>(
                    @"SELECT TemplateName FROM tblProductTemplates").ToList();
            }
        }
    }
}