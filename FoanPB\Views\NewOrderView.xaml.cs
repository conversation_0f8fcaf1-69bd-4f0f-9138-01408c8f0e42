﻿using FoanPB.DataService;
using FoanPB.Models;
using FoanPB.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace FoanPB.Views
{
    /// <summary>
    /// Interaction logic for NewOrderView.xaml
    /// </summary>
    public partial class NewOrderView : UserControl
    {
        public NewOrderView()
        {
            InitializeComponent();
           
        }

        //private void LoadData()
        //{
        //    // Create an instance of Product and add Production Steps
        //    Product product = new Product(1, "Super Widget", "456", "A high-quality widget suitable for a variety of uses.", "Order");
        //    product.ProductionSteps.Add(new ProductionStep(1, 1, 1, "Material Preparation", 30));
        //    product.ProductionSteps.Add(new ProductionStep(2, 1, 2, "Assembly", 45));
        //    product.ProductionSteps.Add(new ProductionStep(3, 1, 3, "Quality Inspection", 15));

        //    // Bind the DataGrid to the ProductionSteps list
        //    dataGridProductionSteps.ItemsSource = product.ProductionSteps;
        //}

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            Button deleteButton = sender as Button;
            if (deleteButton != null)
            {
                // Get the DataGridRow that contains this button
                DataGridRow row = DataGridRow.GetRowContainingElement(deleteButton);

                // Assuming your ItemsSource is a collection that supports removal of items
                //(dataGridProductionSteps.ItemsSource as IList).Remove(row.Item);
            }
        }

        private void ManageOperators_Click(object sender, RoutedEventArgs e)
        {

        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {

        }


        private void SaveTemplateCancel_Click(object sender, RoutedEventArgs e)
        {
            //SaveTemplatePopup.IsOpen = false;
        }

        private void SaveTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            //SaveTemplatePopup.IsOpen = true;
        }

        private void CmbTemplate_GotFocus(object sender, RoutedEventArgs e)
        {
            //CmbTemplate.IsDropDownOpen = true;
        }

        private void CmbTemplate_KeyDown(object sender, KeyEventArgs e)
        {
            //if (CmbTemplate.Text.Length > 0)
                //CmbTemplate.IsDropDownOpen = true;
        }
    }
}
