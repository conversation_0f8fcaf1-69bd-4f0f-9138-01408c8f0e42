﻿using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace FoanPB.Views
{
    public partial class AppliqueView : UserControl
    {
        public AppliqueView()
        {
            InitializeComponent();
        }

        private void StartTime_Click(object sender, RoutedEventArgs e)
        {
        }

        private void EndTime_Click(object sender, RoutedEventArgs e)
        {
        }

        private void Search_Click(object sender, RoutedEventArgs e)
        {
        }

        private void ProductStepsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                Dispatcher.BeginInvoke(async () =>
                {
                    var edited = e.Row.Item as ProductionStep;
                    if (edited != null)
                    {
                        try
                        {
                            var da = new DataAccess();
                            await da.UpdateTimeSpentAsync(edited);

                            // Notify that the ProductionStep TimeSpent has changed, which may affect Product.IsCompleted
                            if (this.DataContext is ViewModels.AppliqueViewModel viewModel)
                            {
                                viewModel.SharedDataService?.NotifyProductionStepTimeSpentChanged(edited);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }, DispatcherPriority.Background);
            }
        }
    }
}