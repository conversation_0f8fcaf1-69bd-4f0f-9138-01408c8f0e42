﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;

namespace FoanPB.ViewModels
{
    public class StepStatistic : BaseViewModel
    {
        private string _stepDescription;
        public string StepDescription
        {
            get => _stepDescription;
            set
            {
                _stepDescription = value;
                OnPropertyChanged(nameof(StepDescription));
            }
        }

        private double _averageTimeSpent;
        public double AverageTimeSpent
        {
            get => _averageTimeSpent;
            set
            {
                _averageTimeSpent = value;
                OnPropertyChanged(nameof(AverageTimeSpent));
                OnPropertyChanged(nameof(AverageTimeSpentFormatted));
            }
        }

        private int _totalSteps;
        public int TotalSteps
        {
            get => _totalSteps;
            set
            {
                _totalSteps = value;
                OnPropertyChanged(nameof(TotalSteps));
            }
        }

        private int _completedSteps;
        public int CompletedSteps
        {
            get => _completedSteps;
            set
            {
                _completedSteps = value;
                OnPropertyChanged(nameof(CompletedSteps));
                OnPropertyChanged(nameof(CompletionPercentage));
            }
        }

        public string AverageTimeSpentFormatted => $"{AverageTimeSpent:F1} min";

        public double CompletionPercentage => TotalSteps > 0 ? (double)CompletedSteps / TotalSteps * 100 : 0;

        public string CompletionPercentageFormatted => $"{CompletionPercentage:F1}%";
    }

    public class OperatorStatistic : BaseViewModel
    {
        private string _operatorName;
        public string OperatorName
        {
            get => _operatorName;
            set
            {
                _operatorName = value;
                OnPropertyChanged(nameof(OperatorName));
            }
        }

        private int _totalTimeSpent;
        public int TotalTimeSpent
        {
            get => _totalTimeSpent;
            set
            {
                _totalTimeSpent = value;
                OnPropertyChanged(nameof(TotalTimeSpent));
                OnPropertyChanged(nameof(TotalTimeSpentFormatted));
                OnPropertyChanged(nameof(TotalTimeSpentHours));
            }
        }

        private int _totalStepsCompleted;
        public int TotalStepsCompleted
        {
            get => _totalStepsCompleted;
            set
            {
                _totalStepsCompleted = value;
                OnPropertyChanged(nameof(TotalStepsCompleted));
                OnPropertyChanged(nameof(AverageTimePerStep));
                OnPropertyChanged(nameof(AverageTimePerStepFormatted));
            }
        }

        public string TotalTimeSpentFormatted => $"{TotalTimeSpent} min";

        public double TotalTimeSpentHours => TotalTimeSpent / 60.0;

        public string TotalTimeSpentHoursFormatted => $"{TotalTimeSpentHours:F1} hrs";

        public double AverageTimePerStep => TotalStepsCompleted > 0 ? (double)TotalTimeSpent / TotalStepsCompleted : 0;

        public string AverageTimePerStepFormatted => $"{AverageTimePerStep:F1} min/step";
    }

    public class StatisticsViewModel : BaseViewModel
    {
        private readonly SharedDataService _sharedDataService;
        private readonly DataAccess _dataAccess;

        private ObservableCollection<StepStatistic> _stepStatistics;
        public ObservableCollection<StepStatistic> StepStatistics
        {
            get => _stepStatistics;
            set
            {
                _stepStatistics = value;
                OnPropertyChanged(nameof(StepStatistics));
            }
        }

        private ObservableCollection<OperatorStatistic> _operatorStatistics;
        public ObservableCollection<OperatorStatistic> OperatorStatistics
        {
            get => _operatorStatistics;
            set
            {
                _operatorStatistics = value;
                OnPropertyChanged(nameof(OperatorStatistics));
            }
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged(nameof(IsBusy));
            }
        }

        private int _totalJobs;
        public int TotalJobs
        {
            get => _totalJobs;
            set
            {
                _totalJobs = value;
                OnPropertyChanged(nameof(TotalJobs));
            }
        }

        private int _completedJobs;
        public int CompletedJobs
        {
            get => _completedJobs;
            set
            {
                _completedJobs = value;
                OnPropertyChanged(nameof(CompletedJobs));
            }
        }

        private double _overallAverageTime;
        public double OverallAverageTime
        {
            get => _overallAverageTime;
            set
            {
                _overallAverageTime = value;
                OnPropertyChanged(nameof(OverallAverageTime));
                OnPropertyChanged(nameof(OverallAverageTimeFormatted));
            }
        }

        public string OverallAverageTimeFormatted => $"{OverallAverageTime:F1} min";

        public ICommand RefreshCommand { get; }

        public StatisticsViewModel(SharedDataService sharedDataService, DataAccess dataAccess)
        {
            _sharedDataService = sharedDataService ?? throw new ArgumentNullException(nameof(sharedDataService));
            _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));

            StepStatistics = new ObservableCollection<StepStatistic>();
            OperatorStatistics = new ObservableCollection<OperatorStatistic>();
            RefreshCommand = new RelayCommand(async () => await RefreshStatisticsAsync(), () => !IsBusy);

            // Listen for changes to the SharedDataService Products collection
            _sharedDataService.PropertyChanged += OnSharedDataServicePropertyChanged;

            // Load initial statistics
            _ = Task.Run(async () => await RefreshStatisticsAsync());
        }

        private void OnSharedDataServicePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SharedDataService.Products))
            {
                // Products collection has been updated, refresh statistics
                _ = Task.Run(async () => await RefreshStatisticsAsync());
            }
        }

        private async Task RefreshStatisticsAsync()
        {
            IsBusy = true;
            try
            {
                await CalculateStepStatistics();
            }
            catch (Exception ex)
            {
                // Handle error appropriately
                System.Windows.MessageBox.Show($"Error refreshing statistics: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task CalculateStepStatistics()
        {
            await Task.Run(() =>
            {
                var products = _sharedDataService.Products;
                if (products == null || !products.Any())
                {
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        StepStatistics.Clear();
                        OperatorStatistics.Clear();
                        TotalJobs = 0;
                        CompletedJobs = 0;
                        OverallAverageTime = 0;
                    });
                    return;
                }

                // Calculate overall job statistics using the new IsCompleted property
                var totalJobs = products.Count;
                var completedJobs = products.Count(p => p.IsCompleted);

                // Group all production steps by step description
                var allSteps = products
                    .Where(p => p.ProductionSteps != null)
                    .SelectMany(p => p.ProductionSteps)
                    .Where(s => !string.IsNullOrWhiteSpace(s.StepDescription))
                    .ToList();

                var stepGroups = allSteps
                    .GroupBy(s => s.StepDescription)
                    .Select(g => new StepStatistic
                    {
                        StepDescription = g.Key,
                        TotalSteps = g.Count(),
                        CompletedSteps = g.Count(s => s.TimeSpent.HasValue && s.TimeSpent > 0),
                        AverageTimeSpent = g.Where(s => s.TimeSpent.HasValue && s.TimeSpent > 0)
                                           .Select(s => s.TimeSpent.Value)
                                           .DefaultIfEmpty(0)
                                           .Average()
                    })
                    .OrderBy(s => s.StepDescription)
                    .ToList();

                // Calculate overall average time across all completed steps
                var allCompletedSteps = allSteps.Where(s => s.TimeSpent.HasValue && s.TimeSpent > 0).ToList();
                var overallAverage = allCompletedSteps.Any() ? allCompletedSteps.Average(s => s.TimeSpent.Value) : 0;

                // Calculate operator statistics
                var operatorGroups = allSteps
                    .Where(s => !string.IsNullOrWhiteSpace(s.OperatorName) && s.TimeSpent.HasValue && s.TimeSpent > 0)
                    .GroupBy(s => s.OperatorName)
                    .Select(g => new OperatorStatistic
                    {
                        OperatorName = g.Key,
                        TotalTimeSpent = (int)g.Sum(s => s.TimeSpent.Value),
                        TotalStepsCompleted = g.Count()
                    })
                    .OrderByDescending(o => o.TotalTimeSpent)
                    .ToList();

                // Update UI on the main thread
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    StepStatistics.Clear();
                    foreach (var stat in stepGroups)
                    {
                        StepStatistics.Add(stat);
                    }

                    OperatorStatistics.Clear();
                    foreach (var stat in operatorGroups)
                    {
                        OperatorStatistics.Add(stat);
                    }

                    TotalJobs = totalJobs;
                    CompletedJobs = completedJobs;
                    OverallAverageTime = overallAverage;
                });
            });
        }
    }
}
