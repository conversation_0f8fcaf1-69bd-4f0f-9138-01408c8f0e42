using System.Windows;

namespace FoanPB.Data
{
    /// <summary>
    /// Event arguments that carry additional information of type T
    /// </summary>
    /// <typeparam name="T">The type of information to carry</typeparam>
    public class FunctionEventArgs<T> : RoutedEventArgs
    {
        /// <summary>
        /// Initializes a new instance with the specified information
        /// </summary>
        /// <param name="info">The information to carry</param>
        public FunctionEventArgs(T info)
        {
            Info = info;
        }

        /// <summary>
        /// Initializes a new instance with the specified routed event and source
        /// </summary>
        /// <param name="routedEvent">The routed event</param>
        /// <param name="source">The source object</param>
        public FunctionEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
        {
        }

        /// <summary>
        /// Gets or sets the information carried by this event args
        /// </summary>
        public T Info { get; set; }
    }
}
