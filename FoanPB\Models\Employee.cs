﻿using FoanPB.ViewModels;

namespace FoanPB.Models
{
    public class Employee : BaseViewModel
    {
        private int _empId;
        private string _empName;
        private int _empDepartmentID;
        private string _empEmail;
        private string _empPhone;
        private bool _isActive;

        public IEnumerable<Department> Departments { get; set; }

        public int EmpId
        {
            get => _empId;
            set
            {
                _empId = value;
                OnPropertyChanged(nameof(EmpId));
            }
        }

        public string EmpName
        {
            get => _empName;
            set
            {
                _empName = value;
                OnPropertyChanged(nameof(EmpName));
            }
        }
        public int EmpDepartmentID
        {
            get => _empDepartmentID;
            set
            {
                _empDepartmentID = value;
                OnPropertyChanged(nameof(EmpDepartmentID));
            }
        }

        public string EmpEmail
        {
            get => _empEmail;
            set
            {
                _empEmail = value;
                OnPropertyChanged(nameof(EmpEmail));
            }
        }

        public string EmpPhone
        {
            get => _empPhone;
            set
            {
                _empPhone = value;
                OnPropertyChanged(nameof(EmpPhone));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public Employee Clone()
        {
            return new Employee
            {
                EmpId = this.EmpId,
                EmpName = this.EmpName,
                EmpDepartmentID = this.EmpDepartmentID,
                EmpEmail = this.EmpEmail,
                EmpPhone = this.EmpPhone,
                IsActive = this.IsActive
            };
        }

    }
}
