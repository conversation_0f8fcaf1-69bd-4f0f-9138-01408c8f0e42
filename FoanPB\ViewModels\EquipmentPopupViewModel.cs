﻿using FoanPB.Commands;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    //public class EquipmentPopupViewModel : BaseViewModel
    //{
    //    private readonly SharedDataService _sharedDataService;
    //    private Equipment _selectedEquipment;
    //    private string _newEquipmentName;

    //    public ObservableCollection<Equipment> Equipment { get; }
    //    public Equipment SelectedEquipment
    //    {
    //        get => _selectedEquipment;
    //        set
    //        {
    //            _selectedEquipment = value;
    //            OnPropertyChanged(nameof(SelectedEquipment));
    //            // Update command states if necessary
    //            //((RelayCommand)SaveCommand).RaiseCanExecuteChanged();
    //            //((RelayCommand)DeleteCommand).RaiseCanExecuteChanged();
    //        }
    //    }

    //    public string NewEquipmentName
    //    {
    //        get => _newEquipmentName;
    //        set
    //        {
    //            _newEquipmentName = value;
    //            OnPropertyChanged(nameof(NewEquipmentName));
    //            //((RelayCommand)SaveCommand).RaiseCanExecuteChanged();
    //        }
    //    }

    //    public ICommand SaveCommand { get; }
    //    public ICommand DeleteCommand { get; }
    //    public ICommand CloseCommand { get; }

    //    public EquipmentPopupViewModel(SharedDataService sharedDataService)
    //    {
    //        _sharedDataService = sharedDataService;
    //        Equipment = _sharedDataService.Equipment;

    //        SaveCommand = new RelayCommand(SaveEquipment, CanSaveEquipment);
    //        DeleteCommand = new RelayCommand(DeleteEquipment, CanModifyEquipment);
    //        CloseCommand = new RelayCommand(CloseWindow);
    //    }

    //    private void SaveEquipment()
    //    {
    //        if (!string.IsNullOrWhiteSpace(NewEquipmentName))
    //        {
    //            var newEquipment = new Equipment { EquipmentName = NewEquipmentName };
    //            _sharedDataService.AddEquipment(newEquipment);
    //            NewEquipmentName = string.Empty;
    //        }
    //        else if (SelectedEquipment != null)
    //        {
    //            _sharedDataService.UpdateEquipment(SelectedEquipment);
    //        }
    //    }

    //    private bool CanSaveEquipment()
    //    {
    //        return !string.IsNullOrWhiteSpace(NewEquipmentName) || SelectedEquipment != null;
    //    }

    //    private void DeleteEquipment()
    //    {
    //        if (SelectedEquipment != null)
    //        {
    //            _sharedDataService.DeleteEquipment(SelectedEquipment.EquipmentId);
    //            SelectedEquipment = null;
    //        }
    //    }

    //    private bool CanModifyEquipment()
    //    {
    //        return SelectedEquipment != null;
    //    }

    //    public event Action RequestClose;

    //    private void CloseWindow()
    //    {
    //        RequestClose?.Invoke();
    //    }
    //}

    public class EquipmentPopupViewModel : BaseViewModel
    {
        private readonly SharedDataService _sharedDataService;
        private Equipment _selectedEquipment;
        private string _newEquipmentName;

        /// <summary>
        /// Collection of Equipment items to be displayed in the DataGrid.
        /// </summary>
        public ObservableCollection<Equipment> Equipment { get; }

        /// <summary>
        /// The currently selected Equipment item in the DataGrid.
        /// </summary>
        public Equipment SelectedEquipment
        {
            get => _selectedEquipment;
            set
            {
                if (_selectedEquipment != value)
                {
                    _selectedEquipment = value;
                    OnPropertyChanged(nameof(SelectedEquipment));
                }
            }
        }

        /// <summary>
        /// The name of the new Equipment to be added.
        /// </summary>
        public string NewEquipmentName
        {
            get => _newEquipmentName;
            set
            {
                if (_newEquipmentName != value)
                {
                    _newEquipmentName = value;
                    OnPropertyChanged(nameof(NewEquipmentName));
                    // Update AddCommand state
                    //((RelayCommand)AddCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Command to add a new Equipment.
        /// </summary>
        public ICommand AddCommand { get; }

        /// <summary>
        /// Command to update the selected Equipment.
        /// </summary>
        public ICommand UpdateCommand { get; }

        /// <summary>
        /// Command to delete the selected Equipment.
        /// </summary>
        public ICommand DeleteCommand { get; }

        /// <summary>
        /// Command to close the Equipment dialog.
        /// (Optional: Not bound in current XAML but can be used if needed)
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Event to request the closing of the Equipment dialog window.
        /// </summary>
        public event Action RequestClose;

        /// <summary>
        /// Initializes a new instance of the EquipmentPopupViewModel.
        /// </summary>
        /// <param name="sharedDataService">The shared data service for data operations.</param>
        public EquipmentPopupViewModel(SharedDataService sharedDataService)
        {
            _sharedDataService = sharedDataService ?? throw new ArgumentNullException(nameof(sharedDataService));
            Equipment = _sharedDataService.Equipment;

            // Initialize commands with corresponding methods and can-execute predicates
            AddCommand = new RelayCommand(AddEquipment, CanAddEquipment);
            UpdateCommand = new RelayCommand(UpdateEquipment, CanUpdateEquipment);
            DeleteCommand = new RelayCommand(DeleteEquipment, CanDeleteEquipment);
            CloseCommand = new RelayCommand(CloseWindow);
        }

        /// <summary>
        /// Adds a new Equipment to the collection and database.
        /// </summary>
        private void AddEquipment()
        {
            if (string.IsNullOrWhiteSpace(NewEquipmentName))
                return;

            try
            {
                var newEquipment = new Equipment
                {
                    EquipmentName = NewEquipmentName.Trim()
                };
                _sharedDataService.AddEquipment(newEquipment);
                NewEquipmentName = string.Empty; // Clear the input after adding
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error adding equipment: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to add new equipment.", ex);
            }
        }

        /// <summary>
        /// Determines whether a new Equipment can be added.
        /// </summary>
        /// <returns>True if NewEquipmentName is not empty or whitespace; otherwise, false.</returns>
        private bool CanAddEquipment()
        {
            return !string.IsNullOrWhiteSpace(NewEquipmentName);
        }

        /// <summary>
        /// Updates the selected Equipment in the collection and database.
        /// </summary>
        private void UpdateEquipment()
        {
            if (SelectedEquipment == null)
                return;

            try
            {
                _sharedDataService.UpdateEquipment(SelectedEquipment);
                // Optionally, provide feedback to the user
                // MessageBox.Show("Equipment updated successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error updating equipment: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to update equipment.", ex);
            }
        }

        /// <summary>
        /// Determines whether the selected Equipment can be updated.
        /// </summary>
        /// <returns>True if an Equipment is selected; otherwise, false.</returns>
        private bool CanUpdateEquipment()
        {
            return SelectedEquipment != null;
        }

        /// <summary>
        /// Deletes the selected Equipment from the collection and database.
        /// </summary>
        private void DeleteEquipment()
        {
            if (SelectedEquipment == null)
                return;

            try
            {
                // Optionally, confirm deletion with the user
                // var result = MessageBox.Show($"Are you sure you want to delete '{SelectedEquipment.EquipmentName}'?", "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                // if (result != MessageBoxResult.Yes)
                //     return;

                _sharedDataService.DeleteEquipment(SelectedEquipment.EquipmentId);
                SelectedEquipment = null; // Clear the selection after deletion
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log the error, show a message to the user)
                // For example:
                // MessageBox.Show($"Error deleting equipment: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw new Exception("Failed to delete equipment.", ex);
            }
        }

        /// <summary>
        /// Determines whether the selected Equipment can be deleted.
        /// </summary>
        /// <returns>True if an Equipment is selected; otherwise, false.</returns>
        private bool CanDeleteEquipment()
        {
            return SelectedEquipment != null;
        }

        /// <summary>
        /// Requests the closing of the Equipment dialog window.
        /// </summary>
        private void CloseWindow()
        {
            RequestClose?.Invoke();
        }
    }
}
