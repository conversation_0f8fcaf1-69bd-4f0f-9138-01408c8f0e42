﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows;
using FoanPB.Models;

namespace FoanPB.Service
{

    public class StatusToButtonVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!(value is ProcessStatus status) || !(parameter is string buttonType))
                return Visibility.Collapsed;

            switch (buttonType)
            {
                default:
                    return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
