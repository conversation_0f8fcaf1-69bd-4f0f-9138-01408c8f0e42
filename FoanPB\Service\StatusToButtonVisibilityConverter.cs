﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows;
using FoanPB.Models;

namespace FoanPB.Service
{
    //public class StatusToButtonVisibilityConverter : IValueConverter
    //{
    //    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        if (!(value is ProcessStatus status) || !(parameter is string buttonType))
    //            return Visibility.Collapsed;

    //        switch (buttonType)
    //        {
    //            case "Start":
    //                return status == ProcessStatus.NotStarted ? Visibility.Visible : Visibility.Collapsed;
    //            case "Pause":
    //                return status == ProcessStatus.Running ? Visibility.Visible : Visibility.Collapsed;
    //            case "Resume":
    //                return status == ProcessStatus.Paused ? Visibility.Visible : Visibility.Collapsed;
    //            case "End":
    //                return status != ProcessStatus.Completed ? Visibility.Visible : Visibility.Collapsed;
    //            default:
    //                return Visibility.Collapsed;
    //        }
    //    }

    //    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
    public class StatusToButtonVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!(value is ProcessStatus status) || !(parameter is string buttonType))
                return Visibility.Collapsed;

            switch (buttonType)
            {
                case "Start":
                    return status == ProcessStatus.NotStarted ? Visibility.Visible : Visibility.Collapsed;
                case "Pause":
                    return status == ProcessStatus.Running ? Visibility.Visible : Visibility.Collapsed;
                case "Resume":
                    return status == ProcessStatus.Paused ? Visibility.Visible : Visibility.Collapsed;
                case "End":
                    // Show End button when status is Running or Paused
                    return (status == ProcessStatus.Running || status == ProcessStatus.Paused)
                        ? Visibility.Visible
                        : Visibility.Collapsed;
                default:
                    return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
