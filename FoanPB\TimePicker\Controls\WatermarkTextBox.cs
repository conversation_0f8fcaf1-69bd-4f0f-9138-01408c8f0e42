using System.Windows;
using System.Windows.Controls;

namespace FoanPB.Controls
{
    /// <summary>
    /// A TextBox control that supports watermark (placeholder) text
    /// </summary>
    public class WatermarkTextBox : TextBox
    {
        static WatermarkTextBox()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(WatermarkTextBox), new FrameworkPropertyMetadata(typeof(WatermarkTextBox)));
        }
        #region Public Properties

        #region Watermark

        /// <summary>
        /// Dependency property for the watermark content
        /// </summary>
        public static readonly DependencyProperty WatermarkProperty = DependencyProperty.Register(
            nameof(Watermark), typeof(object), typeof(WatermarkTextBox), new PropertyMetadata(default(object)));

        /// <summary>
        /// Gets or sets the watermark content displayed when the text box is empty
        /// </summary>
        public object Watermark
        {
            get => GetValue(WatermarkProperty);
            set => SetValue(WatermarkProperty, value);
        }

        #endregion Watermark

        #endregion Public Properties

        /// <summary>
        /// Called when the control receives focus
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected override void OnGotFocus(RoutedEventArgs e)
        {
            base.OnGotFocus(e);
            if (IsEnabled)
            {
                if (!string.IsNullOrEmpty(Text))
                {
                    Select(0, Text.Length);
                }
            }
        }
    }
}
