﻿using FoanPB.Commands;
using FoanPB.DataService;
using FoanPB.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;

namespace FoanPB.ViewModels
{
    public abstract class StepTypeViewModelBase : BaseViewModel
    {
        protected readonly SharedDataService _sharedDataService;
        protected readonly DataAccess _dataAccess;

        public ObservableCollection<Product> Products => _sharedDataService.Products;

        /// <summary>
        /// Exposes the SharedDataService for use by Views that need to notify property changes
        /// </summary>
        public SharedDataService SharedDataService => _sharedDataService;

        private ICollectionView _productsView;
        public ICollectionView ProductsView => _productsView;

        private Product _selectedProduct;
        public virtual Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (_selectedProduct != value)
                {
                    _selectedProduct = value;
                    OnPropertyChanged(nameof(SelectedProduct));
                    // Default behavior: Reset SelectedProductionStep when SelectedProduct changes
                    SelectedProductionStep = null;
                }
            }
        }

        private ProductionStep _selectedProductionStep;
        public virtual ProductionStep SelectedProductionStep
        {
            get => _selectedProductionStep;
            set
            {
                if (_selectedProductionStep != value)
                {
                    _selectedProductionStep = value;
                    OnPropertyChanged(nameof(SelectedProductionStep));
                    if (_selectedProductionStep != null)
                    {
                        InputTimeSpent = _selectedProductionStep.TimeSpent ?? 0;
                    }
                    else
                    {
                        InputTimeSpent = 0;
                        StartTime = null;
                        EndTime = null;
                    }
                }
            }
        }

        private int _inputTimeSpent;
        public int InputTimeSpent
        {
            get => _inputTimeSpent;
            set
            {
                _inputTimeSpent = value;
                OnPropertyChanged(nameof(InputTimeSpent));
            }
        }

        private string _jobNumber;
        public string JobNumber
        {
            get => _jobNumber;
            set
            {
                if (_jobNumber != value)
                {
                    _jobNumber = value;
                    OnPropertyChanged(nameof(JobNumber));
                }
            }
        }

        private string _selectedDetailFilter = "Product Name";
        public string SelectedDetailFilter
        {
            get => _selectedDetailFilter;
            set
            {
                if (_selectedDetailFilter != value)
                {
                    _selectedDetailFilter = value;
                    OnPropertyChanged(nameof(SelectedDetailFilter));
                }
            }
        }

        private string _filterDetailText;
        public string FilterDetailText
        {
            get => _filterDetailText;
            set
            {
                if (_filterDetailText != value)
                {
                    _filterDetailText = value;
                    OnPropertyChanged(nameof(FilterDetailText));
                }
            }
        }

        private string _selectedJobPriority;
        public string SelectedJobPriority
        {
            get => _selectedJobPriority;
            set
            {
                if (_selectedJobPriority != value)
                {
                    _selectedJobPriority = value;
                    OnPropertyChanged(nameof(SelectedJobPriority));
                    if (_productsView != null)
                    {
                        _productsView.Filter = FilterProductsByPriority;
                        _productsView.Refresh();
                    }
                }
            }
        }

        private string _selectedJobType;
        public string SelectedJobType
        {
            get => _selectedJobType;
            set
            {
                if (_selectedJobType != value)
                {
                    _selectedJobType = value;
                    OnPropertyChanged(nameof(SelectedJobType));
                    if (_productsView != null)
                    {
                        _productsView.Filter = FilterProductsByJobType;
                        _productsView.Refresh();
                    }
                }
            }
        }

        private DateTime? _startTime;
        public virtual DateTime? StartTime
        {
            get => _startTime;
            set
            {
                if (_startTime != value)
                {
                    _startTime = value;
                    OnPropertyChanged(nameof(StartTime));
                }
            }
        }

        private DateTime? _endTime;
        public DateTime? EndTime
        {
            get => _endTime;
            set
            {
                if (_endTime != value)
                {
                    _endTime = value;
                    OnPropertyChanged(nameof(EndTime));
                }
            }
        }

        public ObservableCollection<Employee> Operators { get; set; }

        // Shared UI state management
        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged(nameof(IsBusy));
            }
        }

        // Shared commands
        public ICommand SearchByJobNoCommand { get; }
        public ICommand SearchByDetailCommand { get; }
        public ICommand GetStartTimeCommand { get; }
        public ICommand GetEndTimeCommand { get; }
        public ICommand UpdateTimeSpentCommand { get; }
        public ICommand PlusTimeSpentCommand { get; }
        public ICommand MinusTimeSpentCommand { get; }
        public RelayCommand<ProductionStep> StartCommand { get; }

        // Enhanced UI commands
        public ICommand RefreshCommand { get; }
        public ICommand ShowUrgentJobsCommand { get; }
        public ICommand ShowOverDueJobsCommand { get; }
        public ICommand ClearAllFiltersCommand { get; }

        /// <summary>
        /// Gets the count of urgent jobs
        /// </summary>
        public int UrgentJobsCount
        {
            get
            {
                return Products?.Count(p => p.IsUrgent) ?? 0;
            }
        }

        /// <summary>
        /// Gets the count of overdue jobs
        /// </summary>
        public int OverDueJobsCount
        {
            get
            {
                return Products?.Count(p => p.TargetDate.HasValue && p.TargetDate.Value.Date < DateTime.Today) ?? 0;
            }
        }

        protected abstract string TargetStepDescription { get; }

        protected StepTypeViewModelBase(SharedDataService sharedDataService, DataAccess dataAccess)
        {
            _sharedDataService = sharedDataService ?? throw new ArgumentNullException(nameof(sharedDataService));
            _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));

            _productsView = CollectionViewSource.GetDefaultView(Products);
            Operators = new ObservableCollection<Employee>();
            LoadOperator();

            // Listen for changes to the SharedDataService Products collection
            _sharedDataService.PropertyChanged += OnSharedDataServicePropertyChanged;

            StartTime = null;
            EndTime = null;
            InputTimeSpent = 0;

            SearchByJobNoCommand = new RelayCommand(ExecuteSearchByJobNo, CanExecuteSearchByJobNo);
            SearchByDetailCommand = new RelayCommand(ExecuteSearchByDetail, CanExecuteSearchByDetail);

            GetStartTimeCommand = new RelayCommand(ExecuteGetStartTime, CanExecuteStepSpecificCommandForAction);
            GetEndTimeCommand = new RelayCommand(ExecuteGetEndTime, CanExecuteGetEndTimeCommand);

            UpdateTimeSpentCommand = new RelayCommand(async () => await ExecuteUpdateTimeSpentAsync(), CanExecuteStepSpecificCommandForAction);
            PlusTimeSpentCommand = new RelayCommand(async () => await ExecutePlusTimeSpentAsync(), CanExecuteStepSpecificCommandForAction);
            MinusTimeSpentCommand = new RelayCommand(async () => await ExecuteMinusTimeSpentAsync(), CanExecuteStepSpecificCommandForAction);

            StartCommand = new RelayCommand<ProductionStep>(ExecuteStartProcess, CanStartProcess);

            // Enhanced UI commands
            RefreshCommand = new RelayCommand(async () => await ExecuteRefreshAsync(), () => !IsBusy);
            ShowUrgentJobsCommand = new RelayCommand(ShowUrgentJobs);
            ShowOverDueJobsCommand = new RelayCommand(ShowOverDueJobs);
            ClearAllFiltersCommand = new RelayCommand(ClearAllFilters);
        }

        protected virtual bool IsValidStepForProcessing(ProductionStep step)
        {
            return step != null && step.StepDescription == TargetStepDescription;
        }

        private void OnSharedDataServicePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SharedDataService.Products))
            {
                // Products collection has been updated, refresh the view
                RefreshProductsView();
            }
            else if (e.PropertyName == nameof(SharedDataService.Employees))
            {
                // Employees collection has been updated, refresh operators
                LoadOperator();
            }
        }

        protected void LoadOperator()
        {
            Operators.Clear();
            var ops = _sharedDataService.Employees;
            foreach (var op in ops)
            {
                Operators.Add(op);
            }
            OnPropertyChanged(nameof(Operators));
        }

        /// <summary>
        /// Refreshes the ProductsView to bind to the current Products collection.
        /// Call this after the SharedDataService has refreshed its data.
        /// </summary>
        protected void RefreshProductsView()
        {
            // Store current filter and selection state
            var currentFilter = _productsView?.Filter;
            var currentSelectedProduct = SelectedProduct;

            // Recreate the products view with the new collection
            _productsView = CollectionViewSource.GetDefaultView(Products);

            // Restore filter if it existed
            if (currentFilter != null)
            {
                _productsView.Filter = currentFilter;
            }

            // Notify that ProductsView has changed
            OnPropertyChanged(nameof(ProductsView));

            // Clear selections since the old objects may no longer be valid
            SelectedProduct = null;
            SelectedProductionStep = null;

            // Refresh counts when ProductsView is refreshed
            RefreshCounts();
        }

        protected virtual bool FilterProductsByPriority(object obj)
        {
            if (obj is Product product)
            {
                switch (SelectedJobPriority)
                {
                    case "All Jobs": return true;
                    case "Urgent Jobs": return product.IsUrgent;
                    case "Over Due": return product.TargetDate.HasValue && product.TargetDate.Value.Date < DateTime.Today;
                    case "Due Today": return product.TargetDate.HasValue && product.TargetDate.Value.Date == DateTime.Today;
                    case "Due This Week":
                        if (!product.TargetDate.HasValue) return false;
                        var today = DateTime.Today;
                        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
                        var endOfWeek = startOfWeek.AddDays(6);
                        return product.TargetDate.Value.Date >= startOfWeek && product.TargetDate.Value.Date <= endOfWeek;
                    case "Due Next Week":
                        if (!product.TargetDate.HasValue) return false;
                        var todayForNextWeek = DateTime.Today;
                        var startOfNextWeek = todayForNextWeek.AddDays(-(int)todayForNextWeek.DayOfWeek + 7);
                        var endOfNextWeek = startOfNextWeek.AddDays(6);
                        return product.TargetDate.Value.Date >= startOfNextWeek && product.TargetDate.Value.Date <= endOfNextWeek;
                    case "Completed":
                    case "Complete Jobs":
                        // Use the Product's IsCompleted property for overall job completion
                        return product.IsCompleted;
                    case "UnCompleted":
                        // Use the Product's IsCompleted property for overall job completion
                        return !product.IsCompleted;
                    default: return true;
                }
            }
            return false;
        }

        protected virtual bool FilterProductsByJobType(object obj)
        {
            if (obj is Product product)
            {
                if (string.IsNullOrEmpty(SelectedJobType) || SelectedJobType.Equals("All", StringComparison.OrdinalIgnoreCase)) return true;
                if (SelectedJobType.Equals("UnKnown", StringComparison.OrdinalIgnoreCase)) return string.IsNullOrWhiteSpace(product.JobType);
                return product.JobType != null && product.JobType.Equals(SelectedJobType, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        private bool CanExecuteSearchByJobNo(object parameter) => !string.IsNullOrWhiteSpace(JobNumber);
        private void ExecuteSearchByJobNo(object parameter)
        {
            _productsView.Filter = FilterSearchByJobNo;
            _productsView.Refresh();
        }
        private bool FilterSearchByJobNo(object obj)
        {
            if (obj is Product product)
            {
                return string.IsNullOrWhiteSpace(JobNumber) || (product.JobNumber?.IndexOf(JobNumber, StringComparison.OrdinalIgnoreCase) >= 0);
            }
            return false;
        }

        private bool CanExecuteSearchByDetail(object parameter) => !string.IsNullOrWhiteSpace(SelectedDetailFilter) && !string.IsNullOrWhiteSpace(FilterDetailText);
        private void ExecuteSearchByDetail(object parameter)
        {
            _productsView.Filter = FilterSearchByDetail;
            _productsView.Refresh();
        }
        protected virtual bool FilterSearchByDetail(object obj)
        {
            if (obj is Product product)
            {
                if (string.IsNullOrWhiteSpace(SelectedDetailFilter) || string.IsNullOrWhiteSpace(FilterDetailText)) return true;
                string filterTextLower = FilterDetailText.ToLowerInvariant();
                switch (SelectedDetailFilter)
                {
                    case "Product Name": return product.ProductName?.ToLowerInvariant().Contains(filterTextLower) ?? false;
                    case "SKU": return product.SKU?.ToLowerInvariant().Contains(filterTextLower) ?? false;
                    case "Sale Person": return product.Rep?.ToLowerInvariant().Contains(filterTextLower) ?? false;
                    default: return true;
                }
            }
            return false;
        }

        protected virtual bool CanExecuteStepSpecificCommandForAction()
        {
            return SelectedProductionStep != null && IsValidStepForProcessing(SelectedProductionStep);
        }
        protected virtual bool CanExecuteGetEndTimeCommand()
        {
            return CanExecuteStepSpecificCommandForAction() && StartTime.HasValue;
        }
        protected virtual bool CanStartProcess(ProductionStep step) => step != null && IsValidStepForProcessing(step) && step.Status == ProcessStatus.NotStarted;

        protected virtual void ExecuteGetStartTime() => StartTime = DateTime.Now;
        protected virtual void ExecuteGetEndTime()
        {
            EndTime = DateTime.Now;
            RecalculateInputTimeSpent();
        }
        protected void RecalculateInputTimeSpent()
        {
            if (StartTime.HasValue && EndTime.HasValue && EndTime.Value >= StartTime.Value)
            {
                InputTimeSpent = (int)Math.Round((EndTime.Value - StartTime.Value).TotalMinutes);
            }
        }

        protected virtual async Task ExecuteUpdateTimeSpentAsync()
        {
            if (SelectedProductionStep != null && IsValidStepForProcessing(SelectedProductionStep))
            {
                SelectedProductionStep.TimeSpent = InputTimeSpent;
                await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
                _sharedDataService.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
                _productsView.Refresh();
            }
        }
        protected virtual async Task ExecutePlusTimeSpentAsync()
        {
            if (SelectedProductionStep != null && IsValidStepForProcessing(SelectedProductionStep))
            {
                SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) + InputTimeSpent;
                await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
                InputTimeSpent = SelectedProductionStep.TimeSpent ?? 0;
                _sharedDataService.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
                _productsView.Refresh();
            }
        }
        protected virtual async Task ExecuteMinusTimeSpentAsync()
        {
            if (SelectedProductionStep != null && IsValidStepForProcessing(SelectedProductionStep))
            {
                SelectedProductionStep.TimeSpent = (SelectedProductionStep.TimeSpent ?? 0) - InputTimeSpent;
                if (SelectedProductionStep.TimeSpent < 0) SelectedProductionStep.TimeSpent = 0;
                await _dataAccess.UpdateTimeSpentAsync(SelectedProductionStep);
                InputTimeSpent = SelectedProductionStep.TimeSpent ?? 0;
                _sharedDataService.NotifyProductionStepTimeSpentChanged(SelectedProductionStep);
                _productsView.Refresh();
            }
        }

        protected virtual void ExecuteStartProcess(ProductionStep step)
        {
            if (!CanStartProcess(step)) return;
            step.Status = ProcessStatus.Running;
            step.StartTime = DateTime.Now;
            OnPropertyChanged(nameof(SelectedProductionStep));
        }

        // Shared enhanced UI methods
        protected virtual async Task ExecuteRefreshAsync()
        {
            IsBusy = true;
            try
            {
                // Refresh data from the shared data service
                await _sharedDataService.RefreshDataAsync();

                // Refresh the counts after data refresh
                RefreshCounts();
            }
            catch (System.Exception ex)
            {
                // Handle any errors during refresh
                System.Windows.MessageBox.Show($"Error refreshing data: {ex.Message}", "Refresh Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            }
            finally
            {
                IsBusy = false;
            }
        }

        protected virtual void ShowUrgentJobs()
        {
            // Clear other filters and set to show urgent jobs
            SelectedJobPriority = "Urgent Jobs";
        }

        protected virtual void ShowOverDueJobs()
        {
            // Clear other filters and set to show overdue jobs
            SelectedJobPriority = "Over Due";
        }

        protected virtual void ClearAllFilters()
        {
            // Clear all filter properties
            JobNumber = string.Empty;
            SelectedDetailFilter = string.Empty;
            FilterDetailText = string.Empty;
            SelectedJobPriority = "All Jobs";
            SelectedJobType = "All";

            // Refresh the view to show all items
            if (ProductsView != null)
            {
                ProductsView.Filter = null;
                ProductsView.Refresh();
            }
        }

        /// <summary>
        /// Refreshes the count properties when data changes
        /// </summary>
        protected virtual void RefreshCounts()
        {
            OnPropertyChanged(nameof(UrgentJobsCount));
            OnPropertyChanged(nameof(OverDueJobsCount));
        }


    }
}
